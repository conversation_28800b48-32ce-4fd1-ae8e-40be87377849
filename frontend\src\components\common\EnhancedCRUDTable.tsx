import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { Search, Filter, ChevronDown, ChevronUp, MoreHorizontal, Trash2, Edit, Eye } from 'lucide-react';

export interface Column<T> {
  key: keyof T | string;
  label: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, item: T) => React.ReactNode;
  width?: string;
}

export interface Action<T> {
  label: string;
  icon?: React.ReactNode;
  onClick: (item: T) => void;
  variant?: 'primary' | 'secondary' | 'danger';
  disabled?: (item: T) => boolean;
}

export interface EnhancedCRUDTableProps<T extends { id: number }> {
  data: T[];
  columns: Column<T>[];
  actions?: Action<T>[];
  selectedItems?: T[];
  onSelectionChange?: (items: T[]) => void;
  onView?: (item: T) => void;
  onEdit?: (item: T) => void;
  onDelete?: (item: T) => void;
  onCreate?: () => void;
  isLoading?: boolean;
  searchable?: boolean;
  filterable?: boolean;
  sortable?: boolean;
  selectable?: boolean;
  bulkActions?: boolean;
  emptyMessage?: string;
  title?: string;
  createButtonLabel?: string;
  searchPlaceholder?: string;
  className?: string;
  
  // Pagination props
  pagination?: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
  };
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  
  // Filter props
  filters?: Record<string, any>;
  onFiltersChange?: (filters: Record<string, any>) => void;
  
  // Sort props
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  onSortChange?: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
}

function EnhancedCRUDTable<T extends { id: number }>({
  data,
  columns,
  actions = [],
  selectedItems = [],
  onSelectionChange,
  onView,
  onEdit,
  onDelete,
  onCreate,
  isLoading = false,
  searchable = true,
  filterable = false,
  sortable = true,
  selectable = false,
  bulkActions = false,
  emptyMessage,
  title,
  createButtonLabel,
  searchPlaceholder,
  className = '',
  pagination,
  onPageChange,
  onPageSizeChange,
  filters = {},
  onFiltersChange,
  sortBy,
  sortOrder = 'asc',
  onSortChange
}: EnhancedCRUDTableProps<T>) {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [showBulkActions, setShowBulkActions] = useState(false);

  // Default actions
  const defaultActions: Action<T>[] = [];
  
  if (onView) {
    defaultActions.push({
      label: t('common.view'),
      icon: <Eye className="w-4 h-4" />,
      onClick: onView,
      variant: 'secondary'
    });
  }
  
  if (onEdit) {
    defaultActions.push({
      label: t('crud.operations.update'),
      icon: <Edit className="w-4 h-4" />,
      onClick: onEdit,
      variant: 'primary'
    });
  }
  
  if (onDelete) {
    defaultActions.push({
      label: t('crud.operations.delete'),
      icon: <Trash2 className="w-4 h-4" />,
      onClick: onDelete,
      variant: 'danger'
    });
  }

  const allActions = [...defaultActions, ...actions];
  const isAllSelected = selectedItems.length === data.length && data.length > 0;
  const hasSelection = selectedItems.length > 0;

  const handleSelectAll = () => {
    if (isAllSelected) {
      onSelectionChange?.([]);
    } else {
      onSelectionChange?.(data);
    }
  };

  const handleItemSelect = (item: T) => {
    const isSelected = selectedItems.some(selected => selected.id === item.id);
    if (isSelected) {
      onSelectionChange?.(selectedItems.filter(selected => selected.id !== item.id));
    } else {
      onSelectionChange?.([...selectedItems, item]);
    }
  };

  const handleSort = (columnKey: string) => {
    if (!sortable || !onSortChange) return;
    
    const newSortOrder = sortBy === columnKey && sortOrder === 'asc' ? 'desc' : 'asc';
    onSortChange(columnKey, newSortOrder);
  };

  const renderSortIcon = (columnKey: string) => {
    if (!sortable || sortBy !== columnKey) return null;
    
    return sortOrder === 'asc' ? 
      <ChevronUp className="w-4 h-4 ml-1" /> : 
      <ChevronDown className="w-4 h-4 ml-1" />;
  };

  const renderActionButton = (action: Action<T>, item: T, index: number) => {
    const isDisabled = action.disabled?.(item) || false;
    
    return (
      <button
        key={index}
        onClick={() => !isDisabled && action.onClick(item)}
        disabled={isDisabled}
        className={`
          inline-flex items-center px-2 py-1 text-xs font-medium rounded transition-colors
          ${action.variant === 'danger' 
            ? 'text-red-600 hover:text-red-700 hover:bg-red-50' 
            : action.variant === 'primary'
            ? 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
            : 'text-gray-600 hover:text-gray-700 hover:bg-gray-50'
          }
          ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
        title={action.label}
      >
        {action.icon}
        <span className="ml-1">{action.label}</span>
      </button>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2">{t('crud.messages.loading')}</span>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            {title && <h2 className="text-lg font-semibold text-gray-900">{title}</h2>}
            {data.length > 0 && (
              <p className="text-sm text-gray-500 mt-1">
                {t('crud.labels.showingResults', { 
                  start: pagination ? (pagination.page - 1) * pagination.pageSize + 1 : 1,
                  end: pagination ? Math.min(pagination.page * pagination.pageSize, pagination.totalItems) : data.length,
                  total: pagination?.totalItems || data.length
                })}
              </p>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {searchable && (
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder={searchPlaceholder || t('crud.labels.searchPlaceholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            )}
            
            {filterable && (
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <Filter className="w-4 h-4 mr-2" />
                {t('crud.operations.filter')}
              </button>
            )}
            
            {bulkActions && hasSelection && (
              <button
                onClick={() => setShowBulkActions(!showBulkActions)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <MoreHorizontal className="w-4 h-4 mr-2" />
                {t('crud.bulk.bulkActions')}
              </button>
            )}
            
            {onCreate && (
              <button
                onClick={onCreate}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
              >
                {createButtonLabel || t('crud.operations.create')}
              </button>
            )}
          </div>
        </div>
        
        {selectable && hasSelection && (
          <div className="mt-3 text-sm text-gray-600">
            {t('crud.labels.selectedItems', { count: selectedItems.length })}
          </div>
        )}
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {selectable && (
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    checked={isAllSelected}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
              )}
              
              {columns.map((column, index) => (
                <th
                  key={index}
                  className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                    column.sortable && sortable ? 'cursor-pointer hover:bg-gray-100' : ''
                  }`}
                  style={{ width: column.width }}
                  onClick={() => column.sortable && handleSort(String(column.key))}
                >
                  <div className="flex items-center">
                    {column.label}
                    {renderSortIcon(String(column.key))}
                  </div>
                </th>
              ))}
              
              {allActions.length > 0 && (
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('crud.labels.actions')}
                </th>
              )}
            </tr>
          </thead>
          
          <tbody className="bg-white divide-y divide-gray-200">
            {data.length === 0 ? (
              <tr>
                <td 
                  colSpan={columns.length + (selectable ? 1 : 0) + (allActions.length > 0 ? 1 : 0)}
                  className="px-6 py-12 text-center text-gray-500"
                >
                  {emptyMessage || t('crud.labels.emptyState')}
                </td>
              </tr>
            ) : (
              data.map((item) => (
                <tr key={item.id} className="hover:bg-gray-50">
                  {selectable && (
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedItems.some(selected => selected.id === item.id)}
                        onChange={() => handleItemSelect(item)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                  )}
                  
                  {columns.map((column, index) => (
                    <td key={index} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {column.render 
                        ? column.render((item as any)[column.key], item)
                        : String((item as any)[column.key] || '')
                      }
                    </td>
                  ))}
                  
                  {allActions.length > 0 && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        {allActions.map((action, index) => renderActionButton(action, item, index))}
                      </div>
                    </td>
                  )}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && onPageChange && (
        <div className="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
          <div className="flex items-center">
            <span className="text-sm text-gray-700">
              {t('crud.labels.itemsPerPage')}:
            </span>
            <select
              value={pagination.pageSize}
              onChange={(e) => onPageSizeChange?.(Number(e.target.value))}
              className="ml-2 border border-gray-300 rounded px-2 py-1 text-sm"
            >
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onPageChange(1)}
              disabled={pagination.page === 1}
              className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {t('crud.pagination.first')}
            </button>
            <button
              onClick={() => onPageChange(pagination.page - 1)}
              disabled={pagination.page === 1}
              className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {t('crud.pagination.previous')}
            </button>
            
            <span className="text-sm text-gray-700">
              {t('crud.labels.page')} {pagination.page} {t('crud.labels.of')} {pagination.totalPages}
            </span>
            
            <button
              onClick={() => onPageChange(pagination.page + 1)}
              disabled={pagination.page === pagination.totalPages}
              className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {t('crud.pagination.next')}
            </button>
            <button
              onClick={() => onPageChange(pagination.totalPages)}
              disabled={pagination.page === pagination.totalPages}
              className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {t('crud.pagination.last')}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default EnhancedCRUDTable;
