import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../../store/hooks';
import { useLanguage } from '../../../hooks/useLanguage';
import {
  Lightbulb,
  FileText,
  BarChart3,
  Plus,
  MessageSquare,
  BookOpen,
  Star,
  Clock,
  TrendingUp,
  Users,
  Target,
  Calendar,
  Activity,
  Zap,
  Award,
  Brain,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Bar, Line, Doughnut } from 'react-chartjs-2';
import { businessIdeasAPI, BusinessIdea } from '../../../services/incubatorApi';
import { userAPI, UserActivity } from '../../../services/api';
import { analyticsAPI } from '../../../services/analyticsApi';
import { ChartContainer, chartOptions, doughnutOptions, chartColors } from '../../admin/dashboard/charts';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface DashboardData {
  businessIdeas: BusinessIdea[];
  userActivity: UserActivity;
  loading: boolean;
  error: string | null;
}

/**
 * ENHANCED USER DASHBOARD
 *
 * Improved user-focused dashboard with:
 * - Real data from APIs
 * - Interactive charts and visualizations
 * - Live user activity tracking
 * - Business idea progress
 * - Analytics and insights
 */
const UserDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector((state) => state.auth);

  // State for dashboard data
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    businessIdeas: [],
    userActivity: {
      post_count: 0,
      comment_count: 0,
      event_count: 0,
      resource_count: 0,
      likes_received: 0,
      engagement_score: 0,
      posts_by_day: {},
      comments_by_day: {},
      recent_posts: [],
      recent_comments: [],
      recent_events: []
    },
    loading: true,
    error: null
  });

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    if (!user) return;

    setDashboardData(prev => ({ ...prev, loading: true, error: null }));

    try {
      const [businessIdeas, userActivity] = await Promise.all([
        businessIdeasAPI.getBusinessIdeas(),
        userAPI.getUserActivity()
      ]);

      // Filter user's business ideas
      const userBusinessIdeas = businessIdeas.filter(idea => idea.owner.id === user.id);

      setDashboardData({
        businessIdeas: userBusinessIdeas,
        userActivity,
        loading: false,
        error: null
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setDashboardData(prev => ({
        ...prev,
        loading: false,
        error: 'Failed to load dashboard data. Please try again.'
      }));
    }
  };

  // Load data on component mount and user change
  useEffect(() => {
    fetchDashboardData();
  }, [user]);

  // Calculate real user stats
  const getUserStats = () => {
    const { businessIdeas, userActivity } = dashboardData;

    return [
      {
        title: t('dashboard.stats.totalIdeas', 'My Ideas'),
        value: businessIdeas.length.toString(),
        icon: Lightbulb,
        color: 'text-blue-400',
        bgColor: 'bg-blue-500/20',
        description: t('dashboard.stats.ideasDesc', 'Ideas created'),
        change: businessIdeas.length > 0 ? '+' + businessIdeas.length : '0',
        changeType: 'positive' as const
      },
      {
        title: t('dashboard.stats.approved', 'Approved Ideas'),
        value: businessIdeas.filter(idea => idea.moderation_status === 'approved').length.toString(),
        icon: CheckCircle,
        color: 'text-green-400',
        bgColor: 'bg-green-500/20',
        description: t('dashboard.stats.approvedDesc', 'Ideas approved'),
        change: businessIdeas.filter(idea => idea.moderation_status === 'approved').length > 0 ? 'Active' : 'None',
        changeType: 'positive' as const
      },
      {
        title: t('dashboard.userActivity.engagementScore', 'Engagement Score'),
        value: Math.round(userActivity.engagement_score).toString(),
        icon: TrendingUp,
        color: 'text-purple-400',
        bgColor: 'bg-purple-500/20',
        description: t('dashboard.stats.engagementDesc', 'Your activity level'),
        change: userActivity.engagement_score > 50 ? 'High' : userActivity.engagement_score > 20 ? 'Medium' : 'Low',
        changeType: userActivity.engagement_score > 50 ? 'positive' : userActivity.engagement_score > 20 ? 'neutral' : 'negative' as const
      },
      {
        title: t('dashboard.userActivity.postsCreated', 'Posts Created'),
        value: userActivity.post_count.toString(),
        icon: FileText,
        color: 'text-yellow-400',
        bgColor: 'bg-yellow-500/20',
        description: t('dashboard.stats.postsDesc', 'Posts you\'ve created'),
        change: userActivity.post_count > 0 ? '+' + userActivity.post_count : '0',
        changeType: 'positive' as const
      }
    ];
  };

  // Enhanced user actions
  const getUserActions = () => [
    {
      title: t('dashboard.submitNewIdea', 'Submit New Idea'),
      description: t('dashboard.actions.newIdeaDesc', 'Create a new business idea'),
      icon: Plus,
      href: '/dashboard/business-ideas/new',
      color: 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800',
      count: dashboardData.businessIdeas.length
    },
    {
      title: t('dashboard.createBusinessPlan', 'Create Business Plan'),
      description: t('dashboard.actions.templatesDesc', 'Use templates to create plans'),
      icon: FileText,
      href: '/dashboard/templates',
      color: 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800',
      count: dashboardData.businessIdeas.filter(idea => idea.moderation_status === 'approved').length
    },
    {
      title: t('dashboard.aiEnhancedIdea', '🚀 AI-Enhanced Creator'),
      description: t('dashboard.aiEnhancedDescription', 'Create ideas with AI assistance'),
      icon: Brain,
      href: '/chat/enhanced',
      color: 'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800',
      count: Math.round(dashboardData.userActivity.engagement_score)
    },
    {
      title: t('dashboard.findMentor', 'Find Mentor'),
      description: t('dashboard.actions.mentorDesc', 'Connect with experienced mentors'),
      icon: Users,
      href: '/dashboard/mentorship',
      color: 'bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800',
      count: 0
    }
  ];

  // Chart data for user activity
  const getActivityChartData = () => {
    const { userActivity } = dashboardData;
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      return date.toISOString().split('T')[0];
    }).reverse();

    return {
      labels: last7Days.map(date => new Date(date).toLocaleDateString()),
      datasets: [
        {
          label: t('dashboard.userActivity.postsCreated', 'Posts'),
          data: last7Days.map(date => userActivity.posts_by_day[date] || 0),
          backgroundColor: chartColors.blue.light,
          borderColor: chartColors.blue.primary,
          borderWidth: 2,
        },
        {
          label: t('dashboard.userActivity.commentsMade', 'Comments'),
          data: last7Days.map(date => userActivity.comments_by_day[date] || 0),
          backgroundColor: chartColors.purple.light,
          borderColor: chartColors.purple.primary,
          borderWidth: 2,
        }
      ]
    };
  };

  // Chart data for business ideas status
  const getIdeasStatusChartData = () => {
    const { businessIdeas } = dashboardData;
    const approved = businessIdeas.filter(idea => idea.moderation_status === 'approved').length;
    const pending = businessIdeas.filter(idea => idea.moderation_status === 'pending').length;
    const rejected = businessIdeas.filter(idea => idea.moderation_status === 'rejected').length;

    return {
      labels: [
        t('dashboard.stats.approved', 'Approved'),
        t('dashboard.stats.pending', 'Pending'),
        t('dashboard.stats.rejected', 'Rejected')
      ],
      datasets: [
        {
          data: [approved, pending, rejected],
          backgroundColor: [
            chartColors.indigo.primary,
            chartColors.amber.primary,
            chartColors.red.primary,
          ],
          borderColor: [
            chartColors.indigo.primary,
            chartColors.amber.primary,
            chartColors.red.primary,
          ],
          borderWidth: 2,
        }
      ]
    };
  };

  const userStats = getUserStats();
  const userActions = getUserActions();

  if (dashboardData.loading) {
    return (
      <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <RefreshCw className="w-12 h-12 text-purple-400 animate-spin mx-auto mb-4" />
            <p className="text-gray-300 text-lg">{t('common.loading', 'Loading...')}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className={`p-6 md:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="max-w-7xl mx-auto">

          {/* Enhanced Welcome Section */}
          <div className="text-center mb-8">
            <div className="mb-6">
              <h1 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400 mb-2">
                {t('dashboard.welcome', 'Welcome, {{name}}!', { name: user?.username || 'User' })}
              </h1>
              <p className="text-gray-300 text-lg">
                {t('dashboard.overview', 'Your personalized dashboard overview')}
              </p>
            </div>

            <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 shadow-lg border border-white/20 max-w-md mx-auto">
              <div className="text-center">
                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-600/20 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Activity className="w-8 h-8 text-purple-400" />
                </div>
                <h2 className="text-xl font-bold text-white mb-2">
                  {t('dashboard.title', 'Dashboard')}
                </h2>
                <div className="flex items-center justify-center gap-2 text-green-400">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium">
                    {dashboardData.error ? 'Connection Issues' : 'Live Data'}
                  </span>
                </div>
                {dashboardData.error && (
                  <button
                    onClick={fetchDashboardData}
                    className="mt-2 px-3 py-1 bg-purple-600 hover:bg-purple-700 rounded text-sm transition-colors"
                  >
                    <RefreshCw className="w-4 h-4 inline mr-1" />
                    Retry
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Enhanced User Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {userStats.map((stat, index) => (
              <div
                key={index}
                className="bg-black/30 backdrop-blur-sm rounded-lg p-6 shadow-lg border border-white/20 hover:border-white/30 transition-all duration-300 group"
              >
                <div className="text-center">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full ${stat.bgColor} mb-4 group-hover:scale-110 transition-transform ${isRTL ? "flex-row-reverse" : ""}`}>
                    <stat.icon className={`w-6 h-6 ${stat.color}`} />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-1">{stat.value}</h3>
                  <p className="text-gray-300 text-sm font-medium mb-2">{stat.title}</p>
                  <p className="text-gray-400 text-xs mb-3">{stat.description}</p>

                  {/* Change indicator */}
                  <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    stat.changeType === 'positive' ? 'bg-green-500/20 text-green-400' :
                    stat.changeType === 'negative' ? 'bg-red-500/20 text-red-400' :
                    'bg-yellow-500/20 text-yellow-400'
                  }`}>
                    {stat.changeType === 'positive' && <TrendingUp className="w-3 h-3 mr-1" />}
                    {stat.changeType === 'negative' && <TrendingUp className="w-3 h-3 mr-1 rotate-180" />}
                    {stat.change}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* User Activity Chart */}
            <ChartContainer title={t('dashboard.userActivity.title', 'User Activity')}>
              <Bar
                options={{
                  ...chartOptions,
                  plugins: {
                    ...chartOptions.plugins,
                    title: {
                      ...chartOptions.plugins.title,
                      text: t('dashboard.userActivity.recentActivity', 'Last 7 Days Activity'),
                    },
                  },
                }}
                data={getActivityChartData()}
              />
            </ChartContainer>

            {/* Business Ideas Status Chart */}
            <ChartContainer title={t('dashboard.stats.totalIdeas', 'Business Ideas Status')}>
              <Doughnut
                options={{
                  ...doughnutOptions,
                  plugins: {
                    ...doughnutOptions.plugins,
                    title: {
                      ...doughnutOptions.plugins.title,
                      text: t('dashboard.stats.ideasDistribution', 'Ideas by Status'),
                    },
                  },
                }}
                data={getIdeasStatusChartData()}
              />
            </ChartContainer>
          </div>

          {/* Enhanced Quick Actions */}
          <div className="mb-8">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">
                {t('dashboard.quickActions.title', 'Quick Actions')}
              </h2>
              <p className="text-gray-300">
                {t('dashboard.quickActions.subtitle', 'Get started with your entrepreneurial journey')}
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {userActions.map((action, index) => (
                <div key={index} className="bg-black/30 backdrop-blur-sm rounded-lg p-6 shadow-lg border border-white/20 hover:border-white/30 transition-all duration-300 group">
                  <div className="text-center mb-4">
                    <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-purple-600/20 mb-4 group-hover:scale-110 transition-transform ${isRTL ? "flex-row-reverse" : ""}`}>
                      <action.icon className="w-6 h-6 text-purple-400" />
                    </div>
                    <h3 className="text-lg font-bold text-white mb-2">{action.title}</h3>
                    <p className="text-gray-300 text-sm mb-3">{action.description}</p>

                    {/* Action count/status */}
                    <div className="text-xs text-purple-400 mb-4">
                      {action.count !== undefined && (
                        <span className="bg-purple-500/20 px-2 py-1 rounded-full">
                          {action.count} {action.count === 1 ? 'item' : 'items'}
                        </span>
                      )}
                    </div>
                  </div>
                  <a
                    href={action.href}
                    className={`w-full px-4 py-3 ${action.color} rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex justify-center items-center text-white`}
                  >
                    <action.icon className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    {t('common.start', 'Start')}
                  </a>
                </div>
              ))}
            </div>
          </div>

          {/* Recent Activity & Business Ideas */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* Recent Activity */}
            <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 shadow-lg border border-white/20">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className={`inline-flex items-center justify-center w-10 h-10 rounded-full bg-purple-600/20 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <Clock className="w-5 h-5 text-purple-400" />
                  </div>
                  <h3 className="text-xl font-bold text-white">
                    {t('dashboard.recentActivity.title', 'Recent Activity')}
                  </h3>
                </div>
                <button
                  onClick={fetchDashboardData}
                  className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                  title="Refresh"
                >
                  <RefreshCw className="w-4 h-4 text-gray-400" />
                </button>
              </div>

              <div className="space-y-3 max-h-64 overflow-y-auto">
                {dashboardData.userActivity.recent_posts.length > 0 ||
                 dashboardData.userActivity.recent_comments.length > 0 ||
                 dashboardData.userActivity.recent_events.length > 0 ? (
                  <>
                    {dashboardData.userActivity.recent_posts.slice(0, 3).map((post, index) => (
                      <div key={`post-${index}`} className="bg-white/10 border border-white/20 rounded-lg p-3">
                        <div className="flex items-center gap-3">
                          <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                          <div className="flex-1">
                            <p className="text-white text-sm font-medium">
                              Created post: {post.title}
                            </p>
                            <p className="text-gray-400 text-xs">
                              {new Date(post.created_at).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}

                    {dashboardData.userActivity.recent_comments.slice(0, 2).map((comment, index) => (
                      <div key={`comment-${index}`} className="bg-white/10 border border-white/20 rounded-lg p-3">
                        <div className="flex items-center gap-3">
                          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                          <div className="flex-1">
                            <p className="text-white text-sm font-medium">
                              Added comment: {comment.content.substring(0, 50)}...
                            </p>
                            <p className="text-gray-400 text-xs">
                              {new Date(comment.created_at).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </>
                ) : (
                  <div className="text-center py-8">
                    <Activity className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                    <p className="text-gray-300 text-sm">
                      {t('dashboard.userActivity.noRecentActivity', 'No recent activity. Start engaging with the platform!')}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Business Ideas Overview */}
            <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 shadow-lg border border-white/20">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className={`inline-flex items-center justify-center w-10 h-10 rounded-full bg-purple-600/20 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <Lightbulb className="w-5 h-5 text-purple-400" />
                  </div>
                  <h3 className="text-xl font-bold text-white">
                    {t('dashboard.stats.totalIdeas', 'My Business Ideas')}
                  </h3>
                </div>
                <a
                  href="/dashboard/business-ideas"
                  className="text-purple-400 hover:text-purple-300 text-sm font-medium"
                >
                  {t('dashboard.recentActivity.viewMore', 'View All')}
                </a>
              </div>

              <div className="space-y-4 max-h-64 overflow-y-auto">
                {dashboardData.businessIdeas.length > 0 ? (
                  dashboardData.businessIdeas.slice(0, 3).map((idea, index) => (
                    <div key={index} className="bg-white/10 border border-white/20 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="text-white font-medium mb-1">{idea.title}</h4>
                          <p className="text-gray-300 text-sm mb-2 line-clamp-2">
                            {idea.description}
                          </p>
                          <div className="flex items-center gap-4 text-xs">
                            <span className={`px-2 py-1 rounded-full ${
                              idea.moderation_status === 'approved' ? 'bg-green-500/20 text-green-400' :
                              idea.moderation_status === 'pending' ? 'bg-yellow-500/20 text-yellow-400' :
                              'bg-red-500/20 text-red-400'
                            }`}>
                              {idea.moderation_status === 'approved' && <CheckCircle className="w-3 h-3 inline mr-1" />}
                              {idea.moderation_status === 'pending' && <Clock className="w-3 h-3 inline mr-1" />}
                              {idea.moderation_status === 'rejected' && <XCircle className="w-3 h-3 inline mr-1" />}
                              {idea.moderation_status}
                            </span>
                            <span className="text-gray-400">
                              {new Date(idea.created_at).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <Lightbulb className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                    <p className="text-gray-300 text-sm mb-4">
                      {t('dashboard.noIdeas', 'No business ideas yet. Create your first idea!')}
                    </p>
                    <a
                      href="/dashboard/business-ideas/new"
                      className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg text-white text-sm font-medium hover:shadow-glow transition-all duration-300"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      {t('dashboard.submitNewIdea', 'Create New Idea')}
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Platform Features & Insights */}
          <div className="mb-8">
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold text-white mb-2">
                {t('dashboard.features.title', 'Platform Insights')}
              </h3>
              <div className="flex items-center justify-center gap-2 text-green-400">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">Live Analytics</span>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 shadow-lg border border-white/20 hover:border-white/30 transition-all duration-300">
                <div className="text-center">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-blue-600/20 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <Brain className="w-6 h-6 text-blue-400" />
                  </div>
                  <h4 className="font-semibold text-white mb-2">
                    {t('dashboard.features.ai', 'AI Assistant')}
                  </h4>
                  <p className="text-gray-300 text-sm mb-3">
                    {t('dashboard.features.aiDesc', 'Get intelligent guidance for your business ideas')}
                  </p>
                  <div className="text-blue-400 text-xs font-medium bg-blue-500/20 px-2 py-1 rounded-full">
                    {Math.round(dashboardData.userActivity.engagement_score)} engagement score
                  </div>
                </div>
              </div>
              <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 shadow-lg border border-white/20 hover:border-white/30 transition-all duration-300">
                <div className="text-center">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-green-600/20 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <FileText className="w-6 h-6 text-green-400" />
                  </div>
                  <h4 className="font-semibold text-white mb-2">
                    {t('dashboard.features.templates', 'Business Templates')}
                  </h4>
                  <p className="text-gray-300 text-sm mb-3">
                    {t('dashboard.features.templatesDesc', 'Access proven business plan templates')}
                  </p>
                  <div className="text-green-400 text-xs font-medium bg-green-500/20 px-2 py-1 rounded-full">
                    {dashboardData.businessIdeas.length} ideas created
                  </div>
                </div>
              </div>
              <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 shadow-lg border border-white/20 hover:border-white/30 transition-all duration-300">
                <div className="text-center">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-purple-600/20 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <BarChart3 className="w-6 h-6 text-purple-400" />
                  </div>
                  <h4 className="font-semibold text-white mb-2">
                    {t('dashboard.features.analytics', 'Analytics')}
                  </h4>
                  <p className="text-gray-300 text-sm mb-3">
                    {t('dashboard.features.analyticsDesc', 'Track your progress and insights')}
                  </p>
                  <div className="text-purple-400 text-xs font-medium bg-purple-500/20 px-2 py-1 rounded-full">
                    {dashboardData.userActivity.post_count + dashboardData.userActivity.comment_count} activities
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Quick Links */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <a
              href="/dashboard/business-ideas"
              className="bg-black/30 backdrop-blur-sm border border-white/20 hover:border-white/30 rounded-lg p-4 text-center transition-all duration-300 group"
            >
              <div className={`inline-flex items-center justify-center w-10 h-10 rounded-full bg-blue-600/20 mb-3 group-hover:bg-blue-600/30 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}>
                <Lightbulb className="w-5 h-5 text-blue-400" />
              </div>
              <p className="text-white text-sm font-medium mb-1">{t('dashboard.links.ideas', 'Business Ideas')}</p>
              <p className="text-xs text-gray-400">{dashboardData.businessIdeas.length}</p>
            </a>
            <a
              href="/dashboard/templates"
              className="bg-black/30 backdrop-blur-sm border border-white/20 hover:border-white/30 rounded-lg p-4 text-center transition-all duration-300 group"
            >
              <div className={`inline-flex items-center justify-center w-10 h-10 rounded-full bg-green-600/20 mb-3 group-hover:bg-green-600/30 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}>
                <FileText className="w-5 h-5 text-green-400" />
              </div>
              <p className="text-white text-sm font-medium mb-1">{t('dashboard.links.templates', 'Templates')}</p>
              <p className="text-xs text-gray-400">50+</p>
            </a>
            <a
              href="/dashboard/analytics"
              className="bg-black/30 backdrop-blur-sm border border-white/20 hover:border-white/30 rounded-lg p-4 text-center transition-all duration-300 group"
            >
              <div className={`inline-flex items-center justify-center w-10 h-10 rounded-full bg-purple-600/20 mb-3 group-hover:bg-purple-600/30 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}>
                <BarChart3 className="w-5 h-5 text-purple-400" />
              </div>
              <p className="text-white text-sm font-medium mb-1">{t('dashboard.links.analytics', 'Analytics')}</p>
              <p className="text-xs text-gray-400">Live</p>
            </a>
            <a
              href="/chat/enhanced"
              className="bg-black/30 backdrop-blur-sm border border-white/20 hover:border-white/30 rounded-lg p-4 text-center transition-all duration-300 group"
            >
              <div className={`inline-flex items-center justify-center w-10 h-10 rounded-full bg-indigo-600/20 mb-3 group-hover:bg-indigo-600/30 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}>
                <MessageSquare className="w-5 h-5 text-indigo-400" />
              </div>
              <p className="text-white text-sm font-medium mb-1">{t('dashboard.links.ai', 'AI Chat')}</p>
              <p className="text-xs text-gray-400">24/7</p>
            </a>
            <a
              href="/dashboard/mentorship"
              className="bg-black/30 backdrop-blur-sm border border-white/20 hover:border-white/30 rounded-lg p-4 text-center transition-all duration-300 group"
            >
              <div className={`inline-flex items-center justify-center w-10 h-10 rounded-full bg-yellow-600/20 mb-3 group-hover:bg-yellow-600/30 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}>
                <Users className="w-5 h-5 text-yellow-400" />
              </div>
              <p className="text-white text-sm font-medium mb-1">{t('dashboard.findMentor', 'Mentorship')}</p>
              <p className="text-xs text-gray-400">Connect</p>
            </a>
            <a
              href="/dashboard/funding"
              className="bg-black/30 backdrop-blur-sm border border-white/20 hover:border-white/30 rounded-lg p-4 text-center transition-all duration-300 group"
            >
              <div className={`inline-flex items-center justify-center w-10 h-10 rounded-full bg-orange-600/20 mb-3 group-hover:bg-orange-600/30 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}>
                <Target className="w-5 h-5 text-orange-400" />
              </div>
              <p className="text-white text-sm font-medium mb-1">{t('dashboard.exploreFunding', 'Funding')}</p>
              <p className="text-xs text-gray-400">Explore</p>
            </a>
          </div>

        </div>
      </div>
    </div>
  );
};

export default UserDashboard;
