import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import Footer from '../Footer';
// SimpleAINotification removed - using other AI notification components
import { useAppSelector } from '../../store/hooks';
import { useLanguage } from '../../hooks/useLanguage';
import UniversalSidebar from './UniversalSidebar';
import SidebarDiagnostic from '../debug/SidebarDiagnostic';
import RoleDebugPanel from '../debug/RoleDebugPanel';
import { Menu } from 'lucide-react';

interface AuthenticatedLayoutProps {
  children: React.ReactNode;
  showAINotifications?: boolean;
}

/**
 * AuthenticatedLayout - For authenticated pages (dashboard, profile, settings, etc.)
 * Shows: Sidebar + Content + Footer (except on dashboard routes)
 * Does NOT show: Navbar
 */
const AuthenticatedLayout: React.FC<AuthenticatedLayoutProps> = ({
  children,
  showAINotifications = true
}) => {
  const { isRTL } = useLanguage();
  const { isAuthenticated } = useAppSelector(state => state.auth);
  const location = useLocation();
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const [isDesktopSidebarCollapsed, setIsDesktopSidebarCollapsed] = useState(false);

  // Determine if footer should be shown based on current route
  const shouldShowFooter = React.useMemo(() => {
    const currentPath = location.pathname;

    // Hide footer on dashboard routes
    const dashboardRoutes = [
      '/dashboard',
      '/admin',
      '/super_admin',
      '/profile',
      '/settings'
    ];

    // Check if current path starts with any dashboard route
    const isDashboardRoute = dashboardRoutes.some(route =>
      currentPath === route || currentPath.startsWith(route + '/')
    );

    // Debug logging in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Footer visibility check:', {
        currentPath,
        isDashboardRoute,
        shouldShow: !isDashboardRoute
      });
    }

    return !isDashboardRoute;
  }, [location.pathname]);

  return (
    <div className={`min-h-screen flex ${isRTL ? "flex-row-reverse" : ""}`}>
      {/* Universal Sidebar for authenticated users */}
      <>
        {/* Desktop Sidebar - Only visible on large screens */}
        <UniversalSidebar
          variant="desktop"
          isCollapsed={isDesktopSidebarCollapsed}
          onToggle={() => setIsDesktopSidebarCollapsed(!isDesktopSidebarCollapsed)}
        />

        {/* Mobile Sidebar - Re-enabled with updated styling */}
        <UniversalSidebar
          variant="mobile"
          isOpen={isMobileSidebarOpen}
          onClose={() => setIsMobileSidebarOpen(false)}
        />
      </>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-h-screen">
        {/* Mobile Menu Button */}
        <button
          onClick={() => setIsMobileSidebarOpen(true)}
          className={`lg:hidden fixed top-4 ${isRTL ? 'right-4' : 'left-4'} z-50 bg-gradient-to-r from-purple-600 to-blue-600 p-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105`}
        >
          <Menu className="w-6 h-6 text-white" />
        </button>

        {/* Main Content */}
        <main className={`flex-grow ${isRTL ? "flex-row-reverse" : ""} ${
          isRTL
            ? (isDesktopSidebarCollapsed ? 'lg:mr-16' : 'lg:mr-80')
            : (isDesktopSidebarCollapsed ? 'lg:ml-16' : 'lg:ml-80')
        }`}>
          {children}
        </main>

        {/* AI Notification Widget - only for authenticated users */}
        {/* SimpleAINotification removed - using other AI notification components */}

        {/* Sidebar Diagnostic Tool (development only) */}
        <SidebarDiagnostic />

        {/* Role Debug Panel (development only) */}
        <RoleDebugPanel />

        {/* Footer - conditionally rendered based on route */}
        {shouldShowFooter && <Footer />}
      </div>
    </div>
  );
};

export default AuthenticatedLayout;
