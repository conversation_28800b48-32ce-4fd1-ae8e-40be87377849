@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  }

  /* RTL language support */
  html[dir="rtl"] {
    font-family: '<PERSON><PERSON>wal', 'Noto Sans Arabic', system-ui, sans-serif;
  }

  /* Glass morphism background */
  body {
    @apply text-glass-primary bg-gradient-to-b from-slate-900 to-indigo-950;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Smooth transitions for all elements */
  * {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Ensure all elements transition smoothly */
  *, *::before, *::after {
    transition-property: background-color, border-color, color, fill, stroke, box-shadow, opacity;
    transition-duration: 300ms;
    transition-timing-function: ease-in-out;
  }

  /* Exclude certain elements from transitions */
  .no-transition, .no-transition *,
  .animate-pulse, .animate-pulse *,
  .animate-spin, .animate-spin *,
  .skeleton-pulse, .skeleton-pulse * {
    transition: none !important;
  }

  /* Scrollbar styling for webkit browsers */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }

  /* Firefox scrollbar styling */
  * {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
  }
}

@layer components {
  /* Glass morphism card styles */
  .card {
    @apply glass-morphism;
  }

  /* Glass morphism button styles */
  .btn-primary {
    @apply glass-morphism px-4 py-2 font-medium text-glass-primary hover:bg-glass-hover active:bg-glass-active;
  }

  /* Glass morphism input styles */
  .input {
    @apply glass-light px-4 py-3 text-glass-primary placeholder-glass-muted focus:outline-none focus:ring-2 focus:ring-glass-border;
  }

  /* Glass morphism navbar styles */
  .navbar {
    @apply glass-morphism border-b border-glass-border;
  }

  /* Glass morphism modal styles */
  .modal {
    @apply glass-morphism text-glass-primary;
  }

  /* Glass morphism table styles */
  table {
    @apply border-glass-border text-glass-primary;
  }

  th {
    @apply glass-light text-glass-primary;
  }

  td {
    @apply border-glass-border;
  }

  /* Glass morphism section styles */
  .section-primary {
    @apply glass-morphism;
  }

  .section-secondary {
    @apply glass-light;
  }

  /* Glass morphism card styles for different sections */
  .feature-card {
    @apply glass-light;
  }

  .testimonial-card {
    @apply glass-morphism;
  }

  .contact-card {
    @apply glass-light;
  }

  /* Glass morphism text colors */
  .text-primary {
    @apply text-glass-primary;
  }

  .text-secondary {
    @apply text-glass-secondary;
  }

  /* Glass morphism gradient backgrounds */
  .gradient-card {
    @apply glass-morphism bg-gradient-to-r from-glass-bg to-glass-hover;
  }
}

@layer utilities {
  /* Animation utilities */
  .animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-fadeInDelay {
    animation: fadeIn 0.5s ease-in-out 0.2s both;
  }

  .animate-fadeInDelay2 {
    animation: fadeIn 0.5s ease-in-out 0.4s both;
  }

  .animate-fadeInDelay3 {
    animation: fadeIn 0.5s ease-in-out 0.6s both;
  }

  .shadow-glow {
    box-shadow: 0 0 15px rgba(167, 139, 250, 0.5);
  }

  /* AI Space-themed gradient utilities */
  .bg-gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
  }

  .animate-gradient-x {
    background-size: 200% 200%;
    animation: gradient-x 3s ease infinite;
  }

  /* Glass morphism background utilities */
  .theme-bg-primary {
    @apply glass-morphism;
  }

  .theme-bg-secondary {
    @apply glass-light;
  }

  .theme-bg-card {
    @apply glass-morphism;
  }

  .theme-bg-feature {
    @apply glass-light;
  }

  .theme-bg-input {
    @apply glass-light;
  }

  /* Glass morphism text utilities */
  .theme-text-primary {
    @apply text-glass-primary;
  }

  .theme-text-secondary {
    @apply text-glass-secondary;
  }

  .theme-text-accent {
    @apply text-glass-accent;
  }

  /* Glass morphism border utilities */
  .theme-border-primary {
    @apply border-glass-border;
  }

  .theme-border-secondary {
    @apply border-glass-border;
  }

  /* Glass morphism skeleton loading animation */
  .skeleton-pulse {
    @apply relative overflow-hidden glass-light;
  }

  /* Rich text content styles */
  .rich-text-content {
    @apply overflow-hidden;
  }

  .rich-text-content h1 {
    @apply text-2xl font-bold mb-4;
  }

  .rich-text-content h2 {
    @apply text-xl font-bold mb-3;
  }

  .rich-text-content h3 {
    @apply text-lg font-bold mb-2;
  }

  .rich-text-content p {
    @apply mb-4;
  }

  .rich-text-content ul, .rich-text-content ol {
    @apply mb-4 pl-5;
  }

  .rich-text-content ul {
    @apply list-disc;
  }

  .rich-text-content ol {
    @apply list-decimal;
  }

  /* Glass morphism rich text links */
  .rich-text-content a {
    @apply text-glass-accent hover:text-glass-primary underline;
  }

  /* Glass morphism rich text blockquotes */
  .rich-text-content blockquote {
    @apply pl-4 border-l-4 border-glass-border italic my-4 text-glass-secondary;
  }

  .rich-text-content img {
    @apply max-w-full h-auto rounded-lg my-4;
  }

  /* Custom scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply rounded-full;
  }

  /* Glass morphism scrollbar */
  .scrollbar-thumb-glass::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
  }

  .scrollbar-track-glass::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }

  /* Hover effects for scrollbar */
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }

  /* Custom sidebar scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.4);
  }

  /* Responsive utilities */
  .responsive-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4;
  }

  .responsive-flex {
    @apply flex flex-col sm:flex-row gap-4;
  }

  .responsive-padding {
    @apply p-4 sm:p-6 lg:p-8;
  }

  .responsive-margin {
    @apply m-4 sm:m-6 lg:m-8;
  }

  /* Mobile-first responsive text */
  .responsive-text-sm {
    @apply text-sm sm:text-base;
  }

  .responsive-text-base {
    @apply text-base sm:text-lg;
  }

  .responsive-text-lg {
    @apply text-lg sm:text-xl;
  }

  .responsive-text-xl {
    @apply text-xl sm:text-2xl;
  }

  /* Dashboard specific responsive utilities */
  .dashboard-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }

  .dashboard-card {
    @apply glass-morphism p-4 sm:p-6;
  }

  /* .dashboard-sidebar removed - now using UniversalSidebar */

  .dashboard-content {
    @apply w-full lg:flex-1 p-4 sm:p-6 lg:p-8;
  }

  /* Legacy mobile menu classes - replaced by UniversalSidebar mobile implementation */
  /* .mobile-menu-overlay and .mobile-menu removed - now using UniversalSidebar */

  /* Touch-friendly button sizes */
  .touch-button {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  /* Responsive spacing */
  .section-spacing {
    @apply py-8 sm:py-12 lg:py-16;
  }

  .container-spacing {
    @apply px-4 sm:px-6 lg:px-8;
  }

  /* ✅ SIDEBAR LAYOUT FIXES - Ensure content doesn't overlap with fixed sidebar */
  .sidebar-layout-main {
    @apply transition-all duration-300 ease-in-out;
  }

  /* Desktop sidebar spacing - Normal width (320px = w-80) */
  @media (min-width: 1024px) {
    .sidebar-layout-main.with-sidebar {
      margin-left: 320px;
    }

    .sidebar-layout-main.with-sidebar.collapsed {
      margin-left: 64px; /* 64px = w-16 */
    }

    /* RTL support */
    .sidebar-layout-main.with-sidebar.rtl {
      margin-left: 0;
      margin-right: 320px;
    }

    .sidebar-layout-main.with-sidebar.collapsed.rtl {
      margin-left: 0;
      margin-right: 64px;
    }
  }

  /* Mobile - No sidebar margin needed */
  @media (max-width: 1023px) {
    .sidebar-layout-main.with-sidebar,
    .sidebar-layout-main.with-sidebar.collapsed,
    .sidebar-layout-main.with-sidebar.rtl {
      margin-left: 0;
      margin-right: 0;
    }
  }

  /* ✅ ENHANCED SIDEBAR STYLING FIXES - MATCHING LOGIN PAGE DESIGN */
  .universal-sidebar {
    /* Match login page glass morphism style exactly */
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    @apply text-white shadow-2xl border-r border-white/20;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    /* Add gradient overlay matching login page from-gray-900 via-purple-900 to-violet-900 */
    background-image:
      linear-gradient(135deg, rgba(147, 51, 234, 0.15) 0%, rgba(59, 130, 246, 0.15) 100%),
      linear-gradient(180deg, rgba(17, 24, 39, 0.8) 0%, rgba(88, 28, 135, 0.8) 50%, rgba(109, 40, 217, 0.8) 100%);
  }

  .universal-sidebar .nav-item {
    @apply transition-all duration-300 ease-in-out;
    border-radius: 12px;
    border: 1px solid transparent;
  }

  .universal-sidebar .nav-item:hover {
    @apply transform translate-x-1;
    /* Match login page input styling */
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 16px rgba(147, 51, 234, 0.2);
  }

  .universal-sidebar .nav-item.active {
    /* Match login page button gradient: bg-gradient-to-r from-purple-600 to-blue-600 */
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.4) 0%, rgba(59, 130, 246, 0.4) 100%);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    @apply text-white border border-purple-400/50;
    /* Combine multiple shadows for enhanced glow effect like login button */
    box-shadow:
      0 4px 16px rgba(147, 51, 234, 0.4),
      0 0 20px rgba(59, 130, 246, 0.3),
      0 0 15px rgba(167, 139, 250, 0.5);
  }

  /* Custom scrollbar for sidebar - Match login page theme */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.6) 0%, rgba(59, 130, 246, 0.6) 100%);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.8) 0%, rgba(59, 130, 246, 0.8) 100%);
    box-shadow: 0 2px 8px rgba(147, 51, 234, 0.3);
  }

  /* Enhanced glass morphism effects for sidebar */
  .universal-sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
    pointer-events: none;
    z-index: -1;
  }

  /* Sidebar section headers - Match login page title gradient */
  .universal-sidebar h3 {
    /* Match login page title: bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400 */
    background: linear-gradient(135deg, rgb(196, 181, 253) 0%, rgb(96, 165, 250) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  /* Enhanced mobile sidebar styling */
  .universal-sidebar .mobile-backdrop {
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  /* Sidebar toggle button styling */
  .universal-sidebar .toggle-button {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .universal-sidebar .toggle-button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(147, 51, 234, 0.5);
    box-shadow: 0 4px 16px rgba(147, 51, 234, 0.2);
  }

  /* Sidebar header styling - Match login page card */
  .universal-sidebar .sidebar-header {
    /* Match login page card: bg-black/30 backdrop-blur-sm border border-white/20 */
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* App logo in sidebar - Match login page logo styling */
  .universal-sidebar .app-logo {
    /* Match login page gradient logo */
    background: linear-gradient(135deg, rgb(147, 51, 234) 0%, rgb(59, 130, 246) 100%);
    box-shadow: 0 4px 16px rgba(147, 51, 234, 0.3);
  }

  /* Risk badge styling */
  .risk-badge {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
  }

  .risk-badge.critical {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.5);
    color: rgb(252, 165, 165);
  }

  .risk-badge.high {
    background: rgba(245, 101, 101, 0.2);
    border-color: rgba(245, 101, 101, 0.5);
    color: rgb(254, 202, 202);
  }

  .risk-badge.medium {
    background: rgba(251, 191, 36, 0.2);
    border-color: rgba(251, 191, 36, 0.5);
    color: rgb(254, 240, 138);
  }

  /* ✅ PERMISSION-BASED VISIBILITY - Hide elements based on user permissions */
  .permission-hidden {
    @apply hidden;
  }

  .permission-visible {
    @apply block;
  }

  /* ✅ SIDEBAR POSITIONING FIXES */
  .sidebar-fixed {
    @apply fixed top-0 h-full z-40 transition-all duration-300 ease-in-out;
  }

  .sidebar-fixed.left {
    @apply left-0;
  }

  .sidebar-fixed.right {
    @apply right-0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out forwards;
}

.animate-slide-right {
  animation: slideRight 0.3s ease-out forwards;
}

@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}