{"app": {"name": "Yasmeen AI"}, "error": {"notFound": "Page not found", "serverError": "Server error", "networkError": "Network error", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "unknown": "Unknown error occurred"}, "tryAgain": "Try Again", "verifyingAdminAccess": "Verifying admin access...", "search": {"searchPlaceholder": "Search...", "searchResults": "Search Results", "noResults": "No results found", "noResultsFor": "No results found for", "searchFor": "Search for", "searching": "Searching...", "clearSearch": "Clear Search", "advancedSearch": "Advanced Search", "filters": "Filters", "sortBy": "Sort By", "relevance": "Relevance", "date": "Date", "popularity": "Popularity", "rating": "Rating", "newest": "Newest", "oldest": "Oldest", "mostPopular": "Most Popular", "leastPopular": "Least Popular", "highestRated": "Highest Rated", "lowestRated": "Lowest Rated", "ascending": "Ascending", "descending": "Descending", "filterBy": "Filter <PERSON>", "category": "Category", "type": "Type", "status": "Status", "author": "Author", "tags": "Tags", "dateRange": "Date Range", "from": "From", "to": "To", "applyFilters": "Apply Filters", "clearFilters": "Clear Filters", "resetFilters": "Reset Filters", "showingResults": "Showing results", "of": "of", "resultsFound": "results found", "noFiltersApplied": "No filters applied", "filtersApplied": "filters applied", "searchHistory": "Search History", "recentSearches": "Recent Searches", "popularSearches": "Popular Searches", "suggestedSearches": "Suggested Searches", "searchSuggestions": "Search Suggestions", "didYouMean": "Did you mean", "searchTips": "Search Tips", "searchHelp": "Search Help", "exactMatch": "Exact Match", "partialMatch": "Partial Match", "fuzzySearch": "Fuzzy Search", "wildcardSearch": "Wildcard Search", "booleanSearch": "Boolean Search", "phraseSearch": "Phrase Search", "excludeWords": "Exclude Words", "includeWords": "Include Words", "anyOfTheseWords": "Any of these words", "allOfTheseWords": "All of these words", "exactPhrase": "Exact phrase", "noneOfTheseWords": "None of these words", "searchWithin": "Search within", "searchEverywhere": "Search everywhere", "searchInTitle": "Search in title", "searchInContent": "Search in content", "searchInDescription": "Search in description", "searchInTags": "Search in tags", "searchInComments": "Search in comments", "globalSearch": "Global Search", "localSearch": "Local Search", "instantSearch": "Instant Search", "liveSearch": "Live Search", "autoComplete": "Auto Complete", "typeahead": "Typeahead", "searchAsYouType": "Search as you type", "minimumCharacters": "Minimum characters", "maximumResults": "Maximum results", "searchTimeout": "Search timeout", "searchError": "Search error", "searchFailed": "Search failed", "searchUnavailable": "Search unavailable", "searchMaintenance": "Search under maintenance", "tryAgainLater": "Try again later", "refineSearch": "Refine search", "broadenSearch": "Broaden search", "narrowSearch": "Narrow search", "expandSearch": "Expand search", "limitSearch": "Limit search", "customizeSearch": "Customize search", "saveSearch": "Save search", "savedSearches": "Saved searches", "deleteSearch": "Delete search", "shareSearch": "Share search", "exportResults": "Export results", "printResults": "Print results", "emailResults": "Email results", "bookmarkResults": "Bookmark results", "subscribeToSearch": "Subscribe to search", "searchAlerts": "Search alerts", "notifyWhenFound": "Notify when found", "searchFrequency": "Search frequency", "searchSchedule": "Search schedule", "automaticSearch": "Automatic search", "manualSearch": "Manual search", "scheduledSearch": "Scheduled search", "realTimeSearch": "Real-time search", "batchSearch": "Batch search", "bulkSearch": "Bulk search", "multiSearch": "Multi search", "crossSearch": "Cross search", "federatedSearch": "Federated search", "distributedSearch": "Distributed search", "parallelSearch": "Parallel search", "sequentialSearch": "Sequential search", "indexedSearch": "Indexed search", "fullTextSearch": "Full-text search", "semanticSearch": "Semantic search", "contextualSearch": "Contextual search", "intelligentSearch": "Intelligent search", "smartSearch": "Smart search", "adaptiveSearch": "Adaptive search", "personalizedSearch": "Personalized search", "customizedSearch": "Customized search", "tailoredSearch": "Tailored search", "optimizedSearch": "Optimized search", "enhancedSearch": "Enhanced search", "advancedSearchOptions": "Advanced search options", "searchPreferences": "Search preferences", "searchSettings": "Search settings", "searchConfiguration": "Search configuration", "searchParameters": "Search parameters", "searchCriteria": "Search criteria", "searchQuery": "Search query", "searchTerm": "Search term", "searchKeyword": "Search keyword", "searchPhrase": "Search phrase", "searchString": "Search string", "searchPattern": "Search pattern", "searchExpression": "Search expression", "searchFormula": "Search formula", "searchAlgorithm": "Search algorithm", "searchEngine": "Search engine", "searchIndex": "Search index", "searchDatabase": "Search database", "searchCatalog": "Search catalog", "searchDirectory": "Search directory", "searchRepository": "Search repository", "searchLibrary": "Search library", "searchArchive": "Search archive", "searchCollection": "Search collection"}, "searchResources": "Search resources...", "submitting": "Submitting...", "github": "GitHub", "linkedin": "LinkedIn", "twitter": "Twitter", "joinCommunity": "Join the Incubator", "exploreResources": "Explore Incubator Programs", "brief.description.of": "Brief description of what this template is for...", "quick.template": "Quick Template", "quick.template.creator": "Quick Template Creator", "template.created.successfully": "Template created successfully!", "analyze.plan": "Analyze Plan", "business.plan.sections": "Business Plan Sections", "no.section.selected": "No Section Selected", "failed.to.analyze": "Failed to analyze business plan. Please try again.", "generate.with.ai": "Generate with AI", "save.section": "Save Section", "guidance": "Guidance", "system": "System", "actions": {"save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "submit": "Submit", "refresh": "Refresh", "export": "Export", "close": "Close", "confirm": "Confirm", "retry": "Try Again", "createNew": "Create New", "saveChanges": "Save Changes", "loadMore": "Load More"}, "businessIdeas": {"title": "Business Ideas"}, "templates": {"title": "Templates"}, "common": {"optional": "Optional", "returnToHomepage": "Return to Homepage", "technology": "Technology", "healthcare": "Healthcare", "finance": "Finance", "education": "Education", "retail": "Retail", "manufacturing": "Manufacturing", "services": "Services", "other": "Other", "failed": {"to": {"load": "Failed to load"}}, "analyze": {"plan": "Analyze Plan"}, "business": {"planning": "Business Planning"}, "generate": {"with": {"ai": "Generate with AI"}}, "save": "Save", "guidance": "Guidance", "analyzing": {"analyze": {"plan": "Analyzing..."}}, "generating": {"generate": {"with": "Generating..."}}, "saving": {"save": {"section": "Saving..."}}, "idea": {"stage": "Idea Stage"}, "product": {"development": "Product Development"}, "ready": {"to": {"launch": "Ready to Launch"}}, "scaling": "Sc<PERSON>", "status": "Status", "actions": "Actions", "ok": "OK", "description": "Description", "choose": {"from": {"our": "Choose from our collection of professional templates"}}, "general": {"business": "General Business"}, "hospitality": "Hospitality", "nonprofit": "Non-Profit", "advanced": {"ai": {"workflows": "Advanced AI workflows with multi-step reasoning and persistent memory", "intelligence": "Advanced AI Intelligence"}}, "custom": {"ml": {"models": "Custom ML Models"}}, "enhanced": {"chat": {"description": "<PERSON><PERSON><PERSON>"}}, "context": {"aware": {"conversations": "Context-aware conversations with automatic insights integration"}}, "ai": {"models": "AI Models", "availability": "AI Availability"}, "faster": {"analysis": "Faster Analysis"}, "prediction": {"accuracy": "Prediction Accuracy"}, "beyond": {"basic": {"chat": "Beyond basic chat - experience AI as an active business partner that understands, anticipates, and guides your entrepreneurial journey"}}, "from": {"chat": {"to": "From Chat to Intelligence"}}, "before": {"basic": {"chat": "Before: Basic Chat"}}, "after": {"intelligent": {"app": "After: Intelligent App"}}, "recommendation": {"run": {"customer": "Recommendation: Run customer interviews"}}, "immediate": {"benefits": "Immediate Benefits"}, "comprehensive": {"analysis": {"workflows": "Comprehensive analysis workflows for market and competitive insights"}}, "available": "Available", "unavailable": "Unavailable", "online": "Online", "offline": "Offline", "there": "there", "loading": "Loading...", "error": "Error", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "close": "Close", "search": "Search", "submit": "Submit", "back": "Back", "next": "Next", "previous": "Previous", "yes": "Yes", "no": "No", "confirm": "Confirm", "warning": "Warning", "success": "Success", "info": "Information", "email": "Email", "location": "Location", "menu": "<PERSON><PERSON>", "created": "Created", "view": "View", "of": "of", "items": "items", "create": "Create", "accessDenied": "Access Denied", "refresh": "Refresh", "debug": {"info": "Debug Info"}}, "status": {"label": "Status", "active": "Active", "inactive": "Inactive", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "draft": "Draft", "published": "Published", "archived": "Archived", "completed": "Completed", "inProgress": "In Progress", "notStarted": "Not Started", "cancelled": "Cancelled", "delayed": "Delayed", "warning": "Warning", "success": "Success", "info": "Information", "unknown": "Unknown"}, "dates": "Dates", "updated": "Updated", "idea": {"stage": "Idea Stage"}, "business": {"businessIdea": "Business Idea", "businessIdeas": "Business Ideas", "businessPlan": "Business Plan", "businessPlans": "Business Plans", "businessModel": "Business Model", "targetAudience": "Target Audience", "marketOpportunity": "Market Opportunity", "problemStatement": "Problem Statement", "solutionDescription": "Solution Description", "competitiveAnalysis": "Competitive Analysis", "financialProjections": "Financial Projections", "marketAnalysis": "Market Analysis", "executiveSummary": "Executive Summary", "productRoadmap": "Product Roadmap", "technologyStack": "Technology Stack", "sustainabilityPlan": "Sustainability Plan", "internationalExpansion": "International Expansion", "funding": "Funding", "investment": "Investment", "investor": "Investor", "investors": "Investors", "mentor": "Mentor", "mentors": "Mentors", "mentorship": "Mentorship", "incubator": "Incubator", "accelerator": "Accelerator", "startup": "Startup", "entrepreneur": "Entrepreneur", "innovation": "Innovation", "technology": "Technology", "ai": "AI", "artificialIntelligence": "Artificial Intelligence", "machinelearning": "Machine Learning", "deepLearning": "Deep Learning", "neuralNetworks": "Neural Networks", "dataScience": "Data Science", "bigData": "Big Data", "cloudComputing": "Cloud Computing", "blockchain": "Blockchain", "cryptocurrency": "Cryptocurrency", "fintech": "Fintech", "healthtech": "Healthtech", "edtech": "Edtech", "cleantech": "Cleantech", "greentech": "Greentech", "sustainability": "Sustainability", "socialImpact": "Social Impact", "nonprofit": "Nonprofit", "forProfit": "For-Profit", "b2b": "B2B", "b2c": "B2C", "b2g": "B2G", "saas": "SaaS", "paas": "PaaS", "iaas": "IaaS", "marketplace": "Marketplace", "platform": "Platform", "ecosystem": "Ecosystem", "network": "Network", "community": "Community", "collaboration": "Collaboration", "partnership": "Partnership", "alliance": "Alliance", "joint": "Joint Venture", "merger": "Merger", "acquisition": "Acquisition", "ipo": "IPO", "exit": "Exit Strategy", "valuation": "Valuation", "equity": "Equity", "shares": "Shares", "stock": "Stock", "dividend": "Dividend", "revenue": "Revenue", "profit": "Profit", "loss": "Loss", "margin": "<PERSON><PERSON>", "roi": "ROI", "roa": "ROA", "roe": "ROE", "ebitda": "EBITDA", "cashFlow": "Cash Flow", "burnRate": "Burn Rate", "runway": "Runway", "breakeven": "Break-even", "scalability": "Scalability", "growth": "Growth", "expansion": "Expansion", "pivot": "Pivot", "iteration": "Iteration", "validation": "Validation", "testing": "Testing", "prototype": "Prototype", "mvp": "MVP", "poc": "Proof of Concept", "beta": "Beta", "alpha": "Alpha", "launch": "Launch", "prelaunch": "Pre-launch", "postlaunch": "Post-launch", "milestone": "Milestone", "goal": "Goal", "objective": "Objective", "target": "Target", "kpi": "KPI", "metric": "Metric", "analytics": "Analytics", "insights": "Insights", "intelligence": "Intelligence", "reporting": "Reporting", "dashboard": "Dashboard", "visualization": "Visualization", "chart": "Chart", "graph": "Graph", "trend": "Trend", "forecast": "Forecast", "prediction": "Prediction", "projection": "Projection", "scenario": "<PERSON><PERSON><PERSON>", "simulation": "Simulation", "modeling": "Modeling", "optimization": "Optimization", "automation": "Automation", "workflow": "Workflow", "process": "Process", "procedure": "Procedure", "methodology": "Methodology", "framework": "Framework", "strategy": "Strategy", "tactics": "Tactics", "execution": "Execution", "implementation": "Implementation", "deployment": "Deployment", "rollout": "Rollout", "adoption": "Adoption", "integration": "Integration", "migration": "Migration", "transformation": "Transformation", "digitalization": "Digitalization", "modernization": "Modernization", "disruption": "Disruption", "revolution": "Revolution", "evolution": "Evolution", "advancement": "Advancement", "breakthrough": "Breakthrough", "discovery": "Discovery", "invention": "Invention", "creation": "Creation", "development": "Development", "research": "Research", "experimentation": "Experimentation", "exploration": "Exploration", "investigation": "Investigation", "analysis": "Analysis", "evaluation": "Evaluation", "assessment": "Assessment", "review": "Review", "audit": "Audit", "inspection": "Inspection", "monitoring": "Monitoring", "tracking": "Tracking", "measurement": "Measurement", "benchmarking": "Benchmarking", "comparison": "Comparison", "contrast": "Contrast", "differentiation": "Differentiation", "positioning": "Positioning", "branding": "Branding", "marketing": "Marketing", "advertising": "Advertising", "promotion": "Promotion", "campaign": "Campaign", "outreach": "Outreach", "engagement": "Engagement", "interaction": "Interaction", "communication": "Communication", "messaging": "Messaging", "content": "Content", "media": "Media", "social": "Social Media", "digital": "Digital", "online": "Online", "offline": "Offline", "hybrid": "Hybrid", "omnichannel": "Omnichannel", "multichannel": "Multichannel", "crosschannel": "Cross-channel", "synchronization": "Synchronization", "coordination": "Coordination", "alignment": "Alignment", "consistency": "Consistency", "coherence": "Coherence", "unity": "Unity", "harmony": "Harmony", "balance": "Balance", "equilibrium": "Equilibrium", "stability": "Stability", "reliability": "Reliability", "dependability": "Dependability", "trustworthiness": "Trustworthiness", "credibility": "Credibility", "reputation": "Reputation", "brand": "Brand", "image": "Image", "perception": "Perception", "impression": "Impression", "experience": "Experience", "journey": "Journey", "touchpoint": "Touchpoint", "satisfaction": "Satisfaction", "loyalty": "Loyalty", "retention": "Retention", "conversion": "Conversion", "funnel": "Funnel", "pipeline": "Pipeline", "lead": "Lead", "prospect": "Prospect", "customer": "Customer", "client": "Client", "user": "User", "stakeholder": "Stakeholder", "shareholder": "Shareholder", "partner": "Partner", "supplier": "Supplier", "vendor": "<PERSON><PERSON><PERSON>", "contractor": "Contractor", "consultant": "Consultant", "advisor": "Advisor", "expert": "Expert", "specialist": "Specialist", "professional": "Professional", "practitioner": "Practitioner", "executive": "Executive", "manager": "Manager", "director": "Director", "officer": "Officer", "leader": "Leader", "founder": "Founder", "cofounder": "Co-founder", "ceo": "CEO", "cto": "CTO", "cfo": "CFO", "cmo": "CMO", "coo": "COO", "cpo": "CPO", "cso": "CSO", "cdo": "CDO", "cio": "CIO", "chro": "CHRO", "team": "Team", "group": "Group", "department": "Department", "division": "Division", "unit": "Unit", "organization": "Organization", "company": "Company", "corporation": "Corporation", "enterprise": "Enterprise", "business": "Business", "firm": "Firm", "agency": "Agency", "institution": "Institution", "establishment": "Establishment", "entity": "Entity"}, "product": {"development": "Product Development"}, "ready": {"to": {"launch": "Ready to Launch"}}, "scaling": "Sc<PERSON>", "healthcare": "Healthcare", "finance": "Finance", "education": "Education", "technology": "Technology", "manufacturing": "Manufacturing", "retail": "Retail", "other": "Other", "businessIdea": {"stage": "Current Stage", "createTitle": "Create New Business Idea", "subtitle": "AI will help you create a comprehensive business idea", "title": "Business Idea Title", "titlePlaceholder": "Enter a compelling title for your business idea", "industry": "Industry", "description": "Business Idea Description", "descriptionPlaceholder": "Describe your business idea in detail. AI will provide suggestions as you type...", "details": "Business Details", "targetMarket": "Target Market", "targetMarketPlaceholder": "Who are your ideal customers?", "revenueModel": "Revenue Model", "revenueModelPlaceholder": "How will you make money?", "competitiveAdvantage": "Competitive Advantage", "competitiveAdvantagePlaceholder": "What makes you different from competitors?", "fundingNeeded": "Funding Needed", "fundingNeededPlaceholder": "How much funding do you need?", "timeline": "Timeline", "timelinePlaceholder": "When do you plan to launch?", "save": "Save Business Idea"}, "none": "None", "creating": "Creating...", "updating": "Updating...", "deleting": "Deleting...", "posting": "Posting...", "createNewPost": "Create New Post", "createNewEvent": "Create New Event", "thisActionCannot": "This action cannot be undone", "reject": "Reject", "approve": "Approve", "events": "Events", "posts": {"posts": "Posts", "post": "Post", "title": "Title", "content": "Content", "author": "Author", "created": "Created", "updated": "Updated", "category": "Category", "type": "Type", "status": "Status", "tags": "Tags", "comments": "Comments", "likes": "<PERSON>s", "views": "Views", "shares": "Shares", "create": "Create Post", "createNew": "Create New Post", "edit": "Edit Post", "update": "Update Post", "delete": "Delete Post", "publish": "Publish Post", "unpublish": "Unpublish Post", "draft": "Draft", "published": "Published", "archived": "Archived", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "featured": "Featured", "pinned": "Pinned", "locked": "Locked", "hidden": "Hidden", "private": "Private", "public": "Public", "searchPlaceholder": "Search posts...", "filterByCategory": "Filter by category", "filterByAuthor": "Filter by author", "filterByStatus": "Filter by status", "filterByType": "Filter by type", "sortByDate": "Sort by date", "sortByTitle": "Sort by title", "sortByAuthor": "Sort by author", "sortByViews": "Sort by views", "sortByLikes": "Sort by likes", "sortByComments": "Sort by comments", "noPosts": "No posts found", "noPostsDescription": "There are no posts to display", "loadingPosts": "Loading posts...", "loadMorePosts": "Load more posts", "showMorePosts": "Show more posts", "viewPost": "View post", "readMore": "Read more", "readLess": "Read less", "fullPost": "Full post", "postPreview": "Post preview", "postSummary": "Post summary", "postExcerpt": "Post excerpt", "postDetails": "Post details", "postInfo": "Post info", "postMeta": "Post meta", "postStats": "Post stats", "postActions": "Post actions", "postOptions": "Post options", "postSettings": "Post settings", "postPermissions": "Post permissions", "postVisibility": "Post visibility", "postSchedule": "Post schedule", "postFormat": "Post format", "postTemplate": "Post template", "postLayout": "Post layout", "postStyle": "Post style", "postTheme": "Post theme", "postBackground": "Post background", "postImage": "Post image", "postVideo": "Post video", "postAudio": "Post audio", "postFile": "Post file", "postAttachment": "Post attachment", "postMedia": "Post media", "postGallery": "Post gallery", "postSlideshow": "Post slideshow", "postCarousel": "Post carousel", "postEmbed": "Post embed", "postLink": "Post link", "postQuote": "Post quote", "postCode": "Post code", "postTable": "Post table", "postList": "Post list", "postHeading": "Post heading", "postParagraph": "Post paragraph", "postText": "Post text", "postRichText": "Post rich text", "postPlainText": "Post plain text", "postMarkdown": "Post markdown", "postHTML": "Post HTML", "postJSON": "Post JSON", "postXML": "Post XML", "postCSV": "Post CSV", "postPDF": "Post PDF", "postWord": "Post Word", "postExcel": "Post Excel", "postPowerPoint": "Post PowerPoint", "confirmDelete": "Are you sure you want to delete this post?", "deleteError": "Failed to delete post", "deleteSuccess": "Post deleted successfully", "createError": "Failed to create post", "createSuccess": "Post created successfully", "updateError": "Failed to update post", "updateSuccess": "Post updated successfully", "publishError": "Failed to publish post", "publishSuccess": "Post published successfully", "unpublishError": "Failed to unpublish post", "unpublishSuccess": "Post unpublished successfully", "saveError": "Failed to save post", "saveSuccess": "Post saved successfully", "loadError": "Failed to load post", "notFound": "Post not found", "accessDenied": "Access denied", "permissionDenied": "Permission denied", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "validationError": "Validation error", "titleRequired": "Title is required", "contentRequired": "Content is required", "authorRequired": "Author is required", "categoryRequired": "Category is required", "typeRequired": "Type is required", "statusRequired": "Status is required", "titleTooShort": "Title is too short", "titleTooLong": "Title is too long", "contentTooShort": "Content is too short", "contentTooLong": "Content is too long", "invalidTitle": "Invalid title", "invalidContent": "Invalid content", "invalidAuthor": "Invalid author", "invalidCategory": "Invalid category", "invalidType": "Invalid type", "invalidStatus": "Invalid status", "duplicateTitle": "Duplicate title", "duplicateContent": "Duplicate content", "duplicatePost": "Duplicate post", "postExists": "Post already exists", "postNotExists": "Post does not exist", "postDeleted": "Post has been deleted", "postArchived": "Post has been archived", "postExpired": "Post has expired", "postLocked": "Post is locked", "postHidden": "Post is hidden", "postPrivate": "Post is private", "postDraft": "Post is in draft", "postPending": "Post is pending approval", "postRejected": "Post has been rejected", "postUnpublished": "Post is unpublished", "postScheduled": "Post is scheduled", "postPublished": "Post is published", "postFeatured": "Post is featured", "postPinned": "Post is pinned", "postPopular": "Post is popular", "postTrending": "Post is trending", "postRecent": "Post is recent", "postOld": "Post is old", "postNew": "Post is new", "postUpdated": "Post has been updated", "postModified": "Post has been modified", "postEdited": "Post has been edited", "postRevised": "Post has been revised", "postReviewed": "Post is reviewed", "postApproved": "Post has been approved", "postPublic": "Post is public", "postVisible": "Post is visible", "postAccessible": "Post is accessible", "postAvailable": "Post is available", "postActive": "Post is active", "postInactive": "Post is inactive", "postEnabled": "Post is enabled", "postDisabled": "Post is disabled", "postOnline": "Post is online", "postOffline": "Post is offline", "postLive": "Post is live", "postStatic": "Post is static", "postDynamic": "Post is dynamic", "postInteractive": "Post is interactive", "postReadOnly": "Post is read-only", "postEditable": "Post is editable", "postModifiable": "Post is modifiable", "postImmutable": "Post is immutable", "postMutable": "Post is mutable", "postFrozen": "Post is frozen", "postSealed": "Post is sealed", "postProtected": "Post is protected", "postSecure": "Post is secure", "postUnsecure": "Post is unsecure", "postEncrypted": "Post is encrypted", "postDecrypted": "Post is decrypted", "postCompressed": "Post is compressed", "postUncompressed": "Post is uncompressed", "postOptimized": "Post has been optimized", "postUnoptimized": "Post is unoptimized", "postCached": "Post is cached", "postUncached": "Post is uncached", "postIndexed": "Post is indexed", "postUnindexed": "Post is unindexed", "postSearchable": "Post is searchable", "postUnsearchable": "Post is unsearchable", "postDiscoverable": "Post is discoverable", "postUndiscoverable": "Post is undiscoverable", "postRecommended": "Post is recommended", "postUnrecommended": "Post is unrecommended", "postSuggested": "Post is suggested", "postUnsugested": "Post is unsuggested", "postPromoted": "Post is promoted", "postUnpromoted": "Post is unpromoted", "postSponsored": "Post is sponsored", "postUnsponsored": "Post is unsponsored", "postAdvertised": "Post is advertised", "postUnadvertised": "Post is unadvertised", "postMarketed": "Post is marketed", "postUnmarketed": "Post is unmarketted", "postBoosted": "Post is boosted", "postUnboosted": "Post is unboosted", "postHighlighted": "Post is highlighted", "postUnhighlighted": "Post is unhighlighted", "postEmphasized": "Post is emphasized", "postUnemphasized": "Post is unemphasized", "postAccented": "Post is accented", "postUnaccented": "Post is unaccented", "postDecorated": "Post is decorated", "postUndecorated": "Post is undecorated", "postStyled": "Post is styled", "postUnstyled": "Post is unstyled", "postFormatted": "Post is formatted", "postUnformatted": "Post is unformatted", "postStructured": "Post is structured", "postUnstructured": "Post is unstructured", "postOrganized": "Post is organized", "postUnorganized": "Post is unorganized", "postArranged": "Post is arranged", "postUnarranged": "Post is unarranged", "postOrdered": "Post is ordered", "postUnordered": "Post is unordered", "postSorted": "Post is sorted", "postUnsorted": "Post is unsorted", "postGrouped": "Post is grouped", "postUngrouped": "Post is ungrouped", "postCategorized": "Post is categorized", "postUncategorized": "Post is uncategorized", "postClassified": "Post is classified", "postUnclassified": "Post is unclassified", "postTagged": "Post is tagged", "postUntagged": "Post is untagged", "postLabeled": "Post is labeled", "postUnlabeled": "Post is unlabeled", "postMarked": "Post is marked", "postUnmarked": "Post is unmarked", "postFlagged": "Post is flagged", "postUnflagged": "Post is unflagged", "postBookmarked": "Post is bookmarked", "postUnbookmarked": "Post is unbookmarked", "postFavorited": "Post is favorited", "postUnfavorited": "Post is unfavorited", "postLiked": "Post is liked", "postUnliked": "Post is unliked", "postDisliked": "Post is disliked", "postUndisliked": "Post is undisliked", "postUpvoted": "Post is upvoted", "postDownvoted": "Post is downvoted", "postRated": "Post is rated", "postUnrated": "Post is unrated", "postUnreviewed": "Post is unreviewed", "postCommented": "Post is commented", "postUncommented": "Post is uncommented", "postShared": "Post is shared", "postUnshared": "Post is unshared", "postViewed": "Post is viewed", "postUnviewed": "Post is unviewed", "postRead": "Post is read", "postUnread": "Post is unread", "postOpened": "Post is opened", "postUnopenned": "Post is unopened", "postClosed": "Post is closed", "postUnclosed": "Post is unclosed", "postStarted": "Post is started", "postUnstarted": "Post is unstarted", "postFinished": "Post is finished", "postUnfinished": "Post is unfinished", "postCompleted": "Post is completed", "postUncompleted": "Post is uncompleted", "postProcessed": "Post is processed", "postUnprocessed": "Post is unprocessed", "postHandled": "Post is handled", "postUnhandled": "Post is unhandled", "postManaged": "Post is managed", "postUnmanaged": "Post is unmanaged", "postControlled": "Post is controlled", "postUncontrolled": "Post is uncontrolled", "postSupervised": "Post is supervised", "postUnsupervised": "Post is unsupervised", "postMonitored": "Post is monitored", "postUnmonitored": "Post is unmonitored", "postTracked": "Post is tracked", "postUntracked": "Post is untracked", "postLogged": "Post is logged", "postUnlogged": "Post is unlogged", "postRecorded": "Post is recorded", "postUnrecorded": "Post is unrecorded", "postDocumented": "Post is documented", "postUndocumented": "Post is undocumented", "postReported": "Post is reported", "postUnreported": "Post is unreported", "postAnalyzed": "Post is analyzed", "postUnanalyzed": "Post is unanalyzed", "postEvaluated": "Post is evaluated", "postUnevaluated": "Post is unevaluated", "postAssessed": "Post is assessed", "postUnassessed": "Post is unassessed", "postMeasured": "Post is measured", "postUnmeasured": "Post is unmeasured", "postCalculated": "Post is calculated", "postUncalculated": "Post is uncalculated", "postComputed": "Post is computed", "postUncomputed": "Post is uncomputed", "postDetermined": "Post is determined", "postUndetermined": "Post is undetermined", "postEstimated": "Post is estimated", "postUnestimated": "Post is unestimated", "postPredicted": "Post is predicted", "postUnpredicted": "Post is unpredicted", "postForecasted": "Post is forecasted", "postUnforecasted": "Post is unforecasted", "postProjected": "Post is projected", "postUnprojected": "Post is unprojected", "postPlanned": "Post is planned", "postUnplanned": "Post is unplanned", "postUnscheduled": "Post is unscheduled", "postTimedOut": "Post has timed out", "postRenewed": "Post has been renewed", "postExtended": "Post has been extended", "postSuspended": "Post has been suspended", "postResumed": "Post has been resumed", "postPaused": "Post has been paused", "postStopped": "Post has been stopped", "postCanceled": "Post has been canceled", "postAborted": "Post has been aborted", "postTerminated": "Post has been terminated", "postKilled": "<PERSON> has been killed", "postDestroyed": "Post has been destroyed", "postRemoved": "Post has been removed", "postErased": "Post has been erased", "postWiped": "Post has been wiped", "postCleared": "Post has been cleared", "postReset": "Post has been reset", "postRestored": "Post has been restored", "postRecovered": "Post has been recovered", "postRetrieved": "Post has been retrieved", "postFetched": "Post has been fetched", "postLoaded": "Post has been loaded", "postImported": "Post has been imported", "postExported": "Post has been exported", "postTransferred": "Post has been transferred", "postMoved": "Post has been moved", "postCopied": "Post has been copied", "postDuplicated": "Post has been duplicated", "postCloned": "Post has been cloned", "postReplicated": "Post has been replicated", "postMirrored": "Post has been mirrored", "postSynced": "Post has been synced", "postSynchronized": "Post has been synchronized", "postRefreshed": "Post has been refreshed", "postReloaded": "Post has been reloaded", "postChanged": "Post has been changed", "postAltered": "Post has been altered", "postAdjusted": "Post has been adjusted", "postTweaked": "Post has been tweaked", "postFinetuned": "Post has been fine-tuned", "postImproved": "Post has been improved", "postEnhanced": "Post has been enhanced", "postUpgraded": "Post has been upgraded", "postDowngraded": "Post has been downgraded", "postReverted": "Post has been reverted", "postRolledback": "Post has been rolled back", "postUndone": "Post has been undone", "postRedone": "Post has been redone"}, "comments": "Comments", "useTemplate": "Use Template", "browseTemplates": "Browse Templates", "testSystem": "Test System", "dashboard": {"welcome": "Welcome", "overview": "Overview", "stats": "Statistics"}, "createTemplate": "Create Template", "testResults": "Test Results", "newFeaturesTest": "New Features Test Page", "startNow": "Start Now", "getAIRecommendations": "Get AI Recommendations", "viewYourReputation": "View Your Reputation", "backToForums": "Back to Forums", "yourReputation": "Your Reputation", "recentReputationActivity": "Recent Reputation Activity", "forum": "Forum", "currentThread": "Current Thread", "logInTo": "Log In to Reply", "easternTime": "Eastern Time", "pacificTime": "Pacific Time", "london": "London", "dubai": "Dubai", "contentThatRespects": "Content that respects RTL direction", "fileProvides": "File provides", "insteadOfCode": "instead of code", "available": "Available", "unavailable": "Unavailable", "online": "Online", "offline": "Offline", "created": "Created", "resources": {"myResources": "My Resources"}, "validation": {"required": "This field is required", "invalid": "Invalid value", "minLength": "Minimum length is {{min}} characters", "maxLength": "Maximum length is {{max}} characters", "email": "Please enter a valid email address", "password": "Password must meet requirements"}, "auth": {"loginRequired": "Please log in to continue", "accessDenied": "Access denied", "sessionExpired": "Your session has expired"}, "access": {"insufficient": {"permissions": "Insufficient permissions to access this page"}}, "actionButtons": {"create": "Create", "update": "Update", "delete": "Delete", "edit": "Edit", "save": "Save", "cancel": "Cancel", "submit": "Submit", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "import": "Import", "refresh": "Refresh", "loadMore": "Load More", "preview": "Preview", "view": "View", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close"}, "ui": {"all": "All", "analytics": "Analytics", "dashboard": "Dashboard", "profile": "Profile", "settings": "Settings", "notifications": "Notifications", "help": "Help", "support": "Support", "about": "About", "contact": "Contact Us", "privacy": "Privacy Policy", "terms": "Terms of Service", "loading": "Loading...", "saving": "Saving...", "processing": "Processing...", "analyzing": "Analyzing...", "generating": "Generating...", "uploading": "Uploading...", "downloading": "Downloading...", "connecting": "Connecting...", "syncing": "Syncing...", "updating": "Updating...", "deleting": "Deleting...", "creating": "Creating...", "submitting": "Submitting...", "validating": "Validating...", "authenticating": "Authenticating...", "redirecting": "Redirecting...", "refreshing": "Refreshing...", "searching": "Searching...", "filtering": "Filtering...", "sorting": "Sorting...", "exporting": "Exporting...", "importing": "Importing...", "backing": "Backing up...", "restoring": "Restoring...", "archiving": "Archiving...", "publishing": "Publishing...", "scheduling": "Scheduling...", "optimizing": "Optimizing...", "calculating": "Calculating...", "monitoring": "Monitoring...", "tracking": "Tracking...", "recording": "Recording...", "playing": "Playing...", "pausing": "Pausing...", "stopping": "Stopping...", "starting": "Starting...", "initializing": "Initializing...", "configuring": "Configuring...", "installing": "Installing...", "uninstalling": "Uninstalling...", "upgrading": "Upgrading...", "downgrading": "Downgrading...", "migrating": "Migrating...", "synchronizing": "Synchronizing...", "verifying": "Verifying...", "testing": "Testing...", "debugging": "Debugging...", "troubleshooting": "Troubleshooting...", "diagnosing": "Diagnosing...", "repairing": "Repairing...", "fixing": "Fixing...", "resolving": "Resolving...", "completing": "Completing...", "finalizing": "Finalizing...", "preparing": "Preparing...", "building": "Building...", "compiling": "Compiling...", "deploying": "Deploying...", "launching": "Launching...", "activating": "Activating...", "deactivating": "Deactivating...", "enabling": "Enabling...", "disabling": "Disabling...", "locking": "Locking...", "unlocking": "Unlocking...", "securing": "Securing...", "protecting": "Protecting...", "encrypting": "Encrypting...", "decrypting": "Decrypting...", "compressing": "Compressing...", "decompressing": "Decompressing...", "extracting": "Extracting...", "packaging": "Packaging...", "bundling": "Bundling...", "unbundling": "Unbundling...", "merging": "Merging...", "splitting": "Splitting...", "combining": "Combining...", "separating": "Separating...", "grouping": "Grouping...", "ungrouping": "Ungrouping...", "organizing": "Organizing...", "reorganizing": "Reorganizing...", "restructuring": "Restructuring...", "reformatting": "Reformatting...", "converting": "Converting...", "transforming": "Transforming...", "translating": "Translating...", "interpreting": "Interpreting...", "parsing": "Parsing...", "rendering": "Rendering...", "displaying": "Displaying...", "showing": "Showing...", "hiding": "Hiding...", "revealing": "Revealing...", "concealing": "Concealing...", "exposing": "Exposing...", "masking": "Masking...", "unmasking": "Unmasking...", "highlighting": "Highlighting...", "emphasizing": "Emphasizing...", "focusing": "Focusing...", "blurring": "Blurring...", "sharpening": "Sharpening...", "enhancing": "Enhancing...", "improving": "Improving...", "maximizing": "Maximizing...", "minimizing": "Minimizing...", "expanding": "Expanding...", "collapsing": "Collapsing...", "extending": "Extending...", "retracting": "Retracting...", "stretching": "Stretching...", "shrinking": "Shrinking...", "growing": "Growing...", "reducing": "Reducing...", "increasing": "Increasing...", "decreasing": "Decreasing...", "scaling": "Scaling...", "resizing": "Resizing...", "adjusting": "Adjusting...", "calibrating": "Calibrating...", "tuning": "Tuning...", "balancing": "Balancing...", "stabilizing": "Stabilizing...", "normalizing": "Normalizing...", "standardizing": "Standardizing...", "customizing": "Customizing...", "personalizing": "Personalizing...", "tailoring": "Tailoring...", "adapting": "Adapting...", "modifying": "Modifying...", "altering": "Altering...", "changing": "Changing...", "switching": "Switching...", "toggling": "Toggling...", "flipping": "Flipping...", "rotating": "Rotating...", "spinning": "Spinning...", "turning": "Turning...", "moving": "Moving...", "shifting": "Shifting...", "sliding": "Sliding...", "dragging": "Dragging...", "dropping": "Dropping...", "placing": "Placing...", "positioning": "Positioning...", "aligning": "Aligning...", "centering": "Centering...", "justifying": "Justifying...", "distributing": "Distributing...", "spacing": "Spacing...", "padding": "Padding...", "margining": "Margining...", "bordering": "Bordering...", "framing": "Framing...", "wrapping": "Wrapping...", "unwrapping": "Unwrapping...", "menu": "<PERSON><PERSON>", "preview": "Preview", "view": "View"}, "nav": {"settings": "Settings", "menu": "<PERSON><PERSON>", "notifications": "Notifications"}, "ai": {"allRecommendations": "All Recommendations", "alternative": "Alternative", "alternatives": "Alternatives", "analyzingBusinessIdea": "Analyzing Business Idea", "complexity": "Complexity", "generatingRecommendations": "Generating Recommendations", "goodFit": "Good Fit", "marketPotential": "Market Potential", "match": "Match", "noRecommendationsFound": "No Recommendations Found", "perfectMatch": "Perfect Match", "successProbability": "Success Probability", "timeToMarket": "Time to Market", "whyRecommended": "Why Recommended", "availability": "Availability", "features": "AI Features", "platform": "AI Platform", "response": "AI Response", "systemsOnline": "AI Systems Online", "poweredFeatures": "AI-Powered Features", "drivenMentorRecommendations": "AI-driven mentor and advisor recommendations", "poweredInvestorRecommendations": "AI-powered investor and funding recommendations", "poweredVisualAnalysis": "AI-powered visual analysis that can read, understand, and analyze any business document, presentation, or image", "mlAbbreviation": "AI/ML", "businessAdvisor": "24/7 AI business advisor", "accuracy": "94% Accuracy", "startupSuccessForecasting": "87% accuracy in startup success forecasting", "additionalCapabilities": "Additional AI Capabilities", "advancedAnalytics": "Advanced analytics and reporting", "machineLearningModels": "Advanced machine learning models that predict business success, market trends, and investment opportunities with 94% accuracy", "analyzeBusinessIdeas": "Analyze business ideas with AI-powered insights", "analyzeMarketTrends": "Analyze market trends and performance", "analyzingDocument": "Analyzing Document...", "analyzing": "Analyzing...", "arabicChat": "Arabic Chat", "arabicSupport": "Arabic Support", "automatedRecommendations": "Automated business recommendations", "automatedWorkflows": "Automated workflows and business process optimization", "automation": "Automation", "autonomousAdvisor": "Autonomous AI advisor that provides real-time insights, recommendations, and strategic guidance for your business", "available": "Available", "availableInArabicEnglish": "Available in Arabic and English", "analyzeDocument": "Analyze Document", "analyzeText": "Analyze Text", "analyzeTextContent": "Analyze any text content for insights and recommendations", "chatInArabic": "Chat in Arabic with cultural context", "computerVision": "Computer Vision", "coreCapabilities": "Core AI Capabilities", "chartDataExtraction": "Chart data extraction", "documentAnalysisDemo": "Document Analysis Demo", "documentTextExtraction": "Document text extraction", "experiencePower": "Experience the power of Yasmeen AI with live demonstrations of our advanced capabilities", "experienceVoiceCommands": "Experience voice commands in action", "discoverCapabilities": "Discover the comprehensive suite of AI capabilities that will transform your business operations and decision-making process", "fourRevolutionaryTechnologies": "Four revolutionary AI technologies working together for your success", "industryLeadingAccuracy": "Industry-leading AI accuracy for business predictions", "instantAnalysis": "Instant AI analysis and recommendations", "intelligentChat": "Intelligent chat with context-aware responses", "interactiveDemo": "Interactive AI Demo", "internationalMarketAnalysis": "International market analysis and expansion guidance", "multiLanguageSpeechRecognition": "Multi-language speech recognition", "nativeArabicProcessing": "Native Arabic language processing and understanding", "naturalLanguageProcessing": "Natural language processing with Arabic and English support for voice commands, transcription, and synthesis", "newFeaturesAvailable": "New AI features are now available in your dashboard. Explore the enhanced business plan generator", "businessSuccessPredictionDemo": "Business Success Prediction Demo", "businessSuccessProbabilityScoring": "Business success probability scoring", "calculatorsAnalysisTools": "Calculators and analysis tools for investors", "competitiveAnalysis": "Competitive analysis", "competitorIntelligence": "Competitor intelligence", "comprehensiveAnalysisTools": "Comprehensive analysis and risk assessment tools", "comprehensiveInvestmentTools": "Comprehensive investment tools", "comprehensiveRiskAssessment": "Comprehensive risk assessment and mitigation strategies", "comprehensiveTools": "Comprehensive tools for every aspect of your business", "clickToStartVoiceDemo": "Click to start voice demo", "enterTextToAnalyze": "Enter text to analyze...", "fasterDecisions": "Faster Decisions", "monitorServiceHealth": "Monitor AI service health and performance", "pitchDeckAnalysis": "Pitch deck analysis and feedback", "realTimeProcessing": "Real-time Processing", "realTimeData": "Real-time data and insights", "realTimeMarketTrends": "Real-time market trends and opportunity identification", "seeHowPredicts": "See how AI predicts business success", "sentimentAnalysis": "Sentiment analysis", "startUsingCapabilities": "Start using these powerful AI capabilities for your business today", "testBusinessIdea": "Test Business Idea", "testBusinessPlan": "Test Business Plan", "testConsolidatedService": "Test the new consolidated AI service with improved performance and reliability", "testUnifiedChat": "Test the unified chat functionality with automatic fallback", "textAnalysis": "Text Analysis", "textToSpeechSynthesis": "Text-to-speech synthesis", "unifiedServiceDemo": "Unified AI Service Demo", "unifiedChat": "Unified Chat", "voiceAI": "Voice AI", "voiceAIDemo": "Voice AI Demo", "voiceCommandNavigation": "Voice command navigation", "voiceControlledWorkflows": "Voice-controlled workflows", "watchAnalyzeDocuments": "Watch AI analyze business documents", "whyChooseYasmeen": "Why Choose Yasmeen AI?", "logoAndBrandAssessment": "Logo and brand assessment", "meetingTranscription": "Meeting transcription", "visualContentOptimization": "Visual content optimization", "processingSpeeech": "Processing speech...", "processing": "Processing...", "playAudioResponse": "Play Audio Response", "playing": "Playing...", "recordingArabicEnglish": "Recording... Say something in Arabic or English", "recording": "Recording...", "resetDemo": "Reset Demo", "businessPlanDocument": "Business Plan Document", "startVoiceDemo": "Start Voice Demo", "tryDemo": "Try Demo", "watchDemo": "Watch Demo", "capabilities": "AI Capabilities", "recommendations": "AI Recommendations", "analysis": "AI Analysis", "insights": "AI Insights", "predictions": "AI Predictions", "suggestions": "AI Suggestions"}, "profile": {"profile": "Profile", "myProfile": "My Profile", "userProfile": "User Profile", "editProfile": "Edit Profile", "viewProfile": "View Profile", "updateProfile": "Update Profile", "deleteProfile": "Delete Profile", "profileSettings": "Profile Settings", "profileInformation": "Profile Information", "personalInformation": "Personal Information", "professionalInformation": "Professional Information", "contactInformation": "Contact Information", "socialInformation": "Social Information", "publicProfile": "Public Profile", "privateProfile": "Private Profile", "profileVisibility": "Profile Visibility", "profilePrivacy": "Profile Privacy", "profileSecurity": "Profile Security", "profileVerification": "Profile Verification", "profileCompletion": "Profile Completion", "profileProgress": "Profile Progress", "profileStats": "Profile Stats", "profileActivity": "Profile Activity", "profileHistory": "Profile History", "profileTimeline": "Profile Timeline", "profileFeed": "Profile Feed", "profilePosts": "Profile Posts", "profileComments": "Profile Comments", "profileLikes": "Profile Likes", "profileShares": "Profile Shares", "profileFollowers": "Profile Followers", "profileFollowing": "Profile Following", "profileConnections": "Profile Connections", "profileNetwork": "Profile Network", "profileCommunity": "Profile Community", "profileGroups": "Profile Groups", "profileTeams": "Profile Teams", "profileOrganizations": "Profile Organizations", "profileCompanies": "Profile Companies", "profileInstitutions": "Profile Institutions", "profileSchools": "Profile Schools", "profileUniversities": "Profile Universities", "profileCertifications": "Profile Certifications", "profileSkills": "Profile Skills", "profileExpertise": "Profile Expertise", "profileExperience": "Profile Experience", "profileEducation": "Profile Education", "profileAchievements": "Profile Achievements", "profileAwards": "Profile Awards", "profileHonors": "Profile Honors", "profileRecognitions": "Profile Recognitions", "profileTestimonials": "Profile Testimonials", "profileRecommendations": "Profile Recommendations", "profileEndorsements": "Profile Endorsements", "profileReviews": "Profile Reviews", "profileRatings": "Profile Ratings", "profileFeedback": "Profile Feedback", "profilePortfolio": "Profile Portfolio", "profileProjects": "Profile Projects", "profileWork": "Profile Work", "profileSamples": "Profile Samples", "profileGallery": "Profile Gallery", "profileMedia": "Profile Media", "profileImages": "Profile Images", "profileVideos": "Profile Videos", "profileAudio": "Profile Audio", "profileDocuments": "Profile Documents", "profileFiles": "Profile Files", "profileLinks": "Profile Links", "profileWebsites": "Profile Websites", "profileSocialMedia": "Profile Social Media", "profileBlog": "Profile Blog", "profileResume": "Profile Resume", "profileCV": "Profile CV", "profileBio": "Profile Bio", "profileDescription": "Profile Description", "profileSummary": "Profile Summary", "profileOverview": "Profile Overview", "profileIntroduction": "Profile Introduction", "profileAbout": "Profile About", "profileStory": "Profile Story", "profileBackground": "Profile Background", "profileJourney": "Profile Journey", "profileMission": "Profile Mission", "profileVision": "Profile Vision", "profileValues": "Profile Values", "profileGoals": "Profile Goals", "profileObjectives": "Profile Objectives", "profileAspiration": "Profile Aspiration", "profileDreams": "Profile Dreams", "profilePassions": "Profile Passions", "profileInterests": "Profile Interests", "profileHobbies": "Profile Hobbies", "profileActivities": "Profile Activities", "profilePreferences": "Profile Preferences", "profileFavorites": "Profile Favorites", "profileDislikes": "Profile Dislikes", "profilePersonality": "Profile Personality", "profileCharacter": "Profile Character", "profileTraits": "Profile Traits", "profileQualities": "Profile Qualities", "profileStrengths": "Profile Strengths", "profileWeaknesses": "Profile Weaknesses", "profileTalents": "Profile Talents", "profileAbilities": "Profile Abilities", "profileCapabilities": "Profile Capabilities", "profileCompetencies": "Profile Competencies", "profileSpecialties": "Profile Specialties", "profileFocus": "Profile Focus", "profileNiche": "Profile <PERSON>", "profileDomain": "Profile Domain", "profileField": "Profile Field", "profileArea": "Profile Area", "profileSector": "Profile Sector", "profileIndustry": "Profile Industry", "profileMarket": "Profile Market", "profileSegment": "Profile Segment", "profileCategory": "Profile Category", "profileType": "Profile Type", "profileKind": "Profile Kind", "profileClass": "Profile Class", "profileGroup": "Profile Group", "profileSet": "Profile Set", "profileCollection": "Profile Collection", "profileSeries": "Profile Series", "profileRange": "Profile Range", "profileScope": "Profile <PERSON>", "profileExtent": "Profile Extent", "profileReach": "Profile Reach", "profileCoverage": "Profile Coverage", "profileSpan": "Profile Span", "profileBreadth": "Profile Breadth", "profileDepth": "Profile Depth", "profileWidth": "Profile Width", "profileHeight": "Profile Height", "profileLength": "Profile Length", "profileSize": "Profile Size", "profileScale": "Profile Scale", "profileMagnitude": "Profile Magnitude", "profileVolume": "Profile Volume", "profileCapacity": "Profile Capacity", "profileLimit": "Profile Limit", "profileBoundary": "Profile Boundary", "profileEdge": "Profile Edge", "profileBorder": "Profile Border", "profilePerimeter": "Profile Perimeter", "profileCircumference": "Profile Circumference", "profileRadius": "Profile <PERSON>", "profileDiameter": "Profile Diameter", "profileSurface": "Profile Surface", "profileSpace": "Profile Space", "profileRoom": "Profile Room", "profilePlace": "Profile Place", "profileLocation": "Profile Location", "profilePosition": "Profile Position", "profileSite": "Profile Site", "profileSpot": "Profile Spot", "profilePoint": "Profile Point", "profileCoordinate": "Profile Coordinate", "profileAddress": "Profile Address", "profileDestination": "Profile Destination", "profileDirection": "Profile Direction", "profilePath": "Profile Path", "profileRoute": "Profile Route", "profileWay": "Profile Way", "profileRoad": "Profile Road", "profileStreet": "Profile Street", "profileAvenue": "Profile Avenue", "profileBoulevard": "Profile Boulevard", "profileLane": "Profile Lane", "profileAlley": "Profile Alley", "profileCourt": "Profile Court", "profileSquare": "Profile Square", "profileCircle": "Profile Circle", "profileDrive": "Profile Drive", "profileParkway": "Profile Parkway", "profileHighway": "Profile Highway", "profileFreeway": "Profile Freeway", "profileExpressway": "Profile Expressway", "profileTurnpike": "Profile Turnpike", "profileBridge": "Profile Bridge", "profileTunnel": "Profile Tunnel", "profilePass": "Profile Pass", "profileGap": "Profile Gap", "profileValley": "Profile Valley", "profileHill": "Profile Hill", "profileMountain": "Profile Mountain", "profilePeak": "Profile Peak", "profileSummit": "Profile Summit", "profileTop": "Profile Top", "profileBottom": "Profile Bottom", "profileBase": "Profile Base", "profileFoundation": "Profile Foundation", "profileGround": "Profile Ground", "profileFloor": "Profile Floor", "profileCeiling": "Profile Ceiling", "profileRoof": "<PERSON>", "profileWall": "Profile Wall", "profileDoor": "Profile Door", "profileWindow": "Profile Window", "profileGate": "Profile Gate", "profileEntrance": "Profile Entrance", "profileExit": "Profile Exit", "profileOpening": "Profile Opening", "profileClosing": "Profile Closing", "profileStart": "Profile Start", "profileEnd": "Profile End", "profileBeginning": "Profile Beginning", "profileFinish": "Profile Finish", "profileConclusion": "Profile Conclusion", "profileTermination": "Profile Termination", "profileCessation": "Profile Cessation", "profileHalt": "Profile Halt", "profileStop": "Profile Stop", "profilePause": "Profile Pause", "profileBreak": "Profile Break", "profileRest": "Profile Rest", "profileInterval": "Profile Interval", "profileTime": "Profile Time", "profilePeriod": "Profile Period", "profileDuration": "Profile Duration"}, "errors": {"somethingWentWrong": "Something went wrong", "errorOccurred": "An error occurred", "errorDetails": "<PERSON><PERSON><PERSON>", "stackTrace": "Stack Trace", "reloadPage": "Reload Page", "tryAgain": "Try Again", "contactSupport": "Contact Support", "unexpectedError": "An unexpected error occurred", "networkError": "Network Error", "serverError": "Server error occurred", "clientError": "Client error occurred", "validationError": "Validation error occurred", "authenticationError": "Authentication error occurred", "authorizationError": "Authorization error occurred", "notFoundError": "Resource not found", "timeoutError": "Request timeout occurred", "unknownError": "Unknown error occurred", "errorCode": "Error Code", "errorMessage": "Error Message", "errorTime": "Error Time", "reportError": "Report Error", "dismissError": "<PERSON><PERSON><PERSON>", "retryOperation": "Retry Operation", "goBack": "Go Back", "goHome": "Go Home", "refreshPage": "Refresh Page", "clearCache": "<PERSON>ache", "checkConnection": "Check Connection", "enableJavaScript": "Enable JavaScript", "updateBrowser": "Update Browser", "disableExtensions": "Disable Extensions", "clearCookies": "Clear Cookies", "restartApplication": "Restart Application", "reinstallApplication": "Reinstall Application", "contactAdministrator": "Contact Administrator", "submitBugReport": "Submit Bug Report", "viewErrorLog": "View Error Log", "downloadErrorLog": "Download Error Log", "shareErrorDetails": "Share Error Details", "copyErrorDetails": "<PERSON><PERSON>", "emailErrorDetails": "<PERSON><PERSON> Error <PERSON>", "printErrorDetails": "Print Error Details", "saveErrorDetails": "Save Error <PERSON>", "exportErrorDetails": "Export Error Details", "importErrorDetails": "Import Error Details", "analyzeError": "Analy<PERSON>", "debugError": "Debug Error", "fixError": "<PERSON>x <PERSON>", "preventError": "Prevent Error", "monitorError": "Monitor Error", "trackError": "Track Error", "logError": "Log <PERSON>r", "recordError": "Record Error", "captureError": "Capture Error", "handleError": "<PERSON><PERSON>", "processError": "Process Error", "resolveError": "Resolve <PERSON>r", "escalateError": "Escalate <PERSON>", "acknowledgeError": "Acknowledge Error", "ignoreError": "Ignore <PERSON>", "suppressError": "Suppress Error", "hideError": "<PERSON><PERSON>", "showError": "Show Error", "displayError": "<PERSON><PERSON><PERSON>rror", "renderError": "Render Error", "formatError": "Format Error", "parseError": "<PERSON><PERSON>", "validateError": "Validate Error", "sanitizeError": "Sanitize Error", "encodeError": "Encode Error", "decodeError": "Decode Error", "compressError": "Comp<PERSON>", "decompressError": "Decompress <PERSON>", "encryptError": "Encrypt Error", "decryptError": "Decrypt <PERSON>r", "hashError": "<PERSON><PERSON>", "signError": "Sign Error", "verifyError": "<PERSON><PERSON><PERSON>", "authenticateError": "Authenticate Error", "authorizeError": "Authorize Error", "permissionError": "Permission Error", "accessError": "Access Error", "securityError": "Security Error", "privacyError": "Priva<PERSON>", "complianceError": "Compliance Error", "regulatoryError": "Regulatory Error", "legalError": "Legal Error", "ethicalError": "Ethical Error", "moralError": "<PERSON><PERSON> Error", "socialError": "Social Error", "culturalError": "Cultural Error", "linguisticError": "Linguistic Error", "semanticError": "<PERSON><PERSON><PERSON>", "syntacticError": "Syntactic Error", "lexicalError": "Lexical Error", "grammaticalError": "Grammatical Error", "spellingError": "Spelling <PERSON><PERSON><PERSON>", "typographicalError": "Typographical Error", "formattingError": "Formatting Error", "layoutError": "Layout Error", "designError": "Design Error", "styleError": "Style Error", "themeError": "Theme Error", "colorError": "Color Error", "fontError": "Font Error", "imageError": "Image Error", "videoError": "Video Error", "audioError": "Audio Error", "mediaError": "Media Error", "fileError": "File Error", "documentError": "Document Error", "dataError": "Data Error", "databaseError": "Database Error", "queryError": "Query Error", "transactionError": "Transaction Error", "connectionError": "Connection Error", "communicationError": "Communication Error", "protocolError": "Protocol Error", "interfaceError": "Interface <PERSON>", "apiError": "API Error", "serviceError": "Service Error", "systemError": "System Error", "applicationError": "Application Error", "softwareError": "Software Error", "hardwareError": "Hardware Error", "firmwareError": "Firmware Error", "driverError": "Driver Error", "kernelError": "<PERSON><PERSON>", "operatingSystemError": "Operating System Error", "platformError": "Platform Error", "environmentError": "Environment Error", "configurationError": "Configuration Error", "setupError": "Setup E<PERSON>r", "installationError": "Installation Error", "deploymentError": "Deployment Error", "buildError": "Build Error", "compileError": "<PERSON><PERSON><PERSON>", "linkError": "<PERSON>", "loadError": "<PERSON><PERSON>", "runtimeError": "Runtime Error", "executionError": "Execution Error", "performanceError": "Performance Error", "memoryError": "Memory Error", "storageError": "Storage Error", "diskError": "Disk Error", "cpuError": "CPU Error", "gpuError": "GPU Error", "internetError": "Internet Error", "wifiError": "<PERSON><PERSON><PERSON><PERSON>", "bluetoothError": "Bluetooth Error", "usbError": "USB Error", "serialError": "Serial Error", "parallelError": "<PERSON><PERSON><PERSON>", "ethernetError": "<PERSON><PERSON><PERSON>", "firewallError": "Firewall Error", "proxyError": "Proxy Error", "vpnError": "VPN Error", "dnsError": "DNS Error", "dhcpError": "DHCP Error", "tcpError": "TCP Error", "udpError": "UDP Error", "httpError": "HTTP Error", "httpsError": "HTTPS Error", "ftpError": "FTP Error", "sftpError": "SFTP Error", "sshError": "SSH Error", "telnetError": "Telnet Error", "smtpError": "SMTP Error", "pop3Error": "POP3 Error", "imapError": "IMAP Error", "ldapError": "LDAP Error", "sqlError": "SQL Error", "xmlError": "XML Error", "jsonError": "JSON Error", "yamlError": "YAML Error", "csvError": "CSV Error", "htmlError": "HTML Error", "cssError": "CSS Error", "javascriptError": "JavaScript Error", "typescriptError": "TypeScript Error", "pythonError": "Python Error", "javaError": "Java Error", "csharpError": "C# Error", "cplusplusError": "C++ Error", "cError": "C Error", "phpError": "PHP Error", "rubyError": "<PERSON>", "goError": "<PERSON>r", "rustError": "Rust <PERSON>r", "swiftError": "Swift Error", "kotlinError": "<PERSON><PERSON><PERSON>", "scalaError": "Scala Error", "haskellError": "<PERSON><PERSON>", "erlangError": "<PERSON><PERSON><PERSON>", "elixirError": "<PERSON><PERSON><PERSON>", "clojureError": "<PERSON><PERSON><PERSON>", "lispError": "Lisp Error", "prologError": "Prolog Error", "fortranError": "<PERSON><PERSON>", "cobolError": "COBOL Error", "assemblyError": "Assembly Error", "machineCodeError": "Machine Code Error", "binaryError": "Binary Error", "hexadecimalError": "Hexadecimal Error", "octalError": "Octal Error", "decimalError": "Decimal Error", "floatingPointError": "Floating Point Error", "integerError": "<PERSON><PERSON><PERSON>", "stringError": "String Error", "characterError": "Character Error", "booleanError": "Boolean Error", "arrayError": "<PERSON><PERSON><PERSON>", "listError": "List Error", "setError": "<PERSON>rror", "mapError": "Map Error", "dictionaryError": "Dictionary Error", "hashTableError": "Hash Table Error", "treeError": "Tree Error", "graphError": "Graph Error", "stackError": "<PERSON><PERSON>", "queueError": "<PERSON><PERSON>", "heapError": "<PERSON><PERSON>rror", "priorityQueueError": "Priority <PERSON><PERSON>r", "linkedListError": "Linked List Error", "doublyLinkedListError": "Doubly Linked List Error", "circularLinkedListError": "Circular Linked List Error", "binaryTreeError": "Binary Tree Error", "binarySearchTreeError": "Binary Search Tree Error", "avlTreeError": "AVL Tree Error", "redBlackTreeError": "Red-Black Tree Error", "bTreeError": "B-Tree Error", "trieError": "<PERSON><PERSON>", "suffixTreeError": "Suffix Tree Error", "segmentTreeError": "Segment Tree Error", "fenwickTreeError": "Fenwick Tree Error", "disjointSetError": "Disjoint Set Error", "bloomFilterError": "<PERSON> Filter <PERSON>", "skipListError": "Skip <PERSON> Error", "fibonacciHeapError": "<PERSON><PERSON><PERSON><PERSON> Heap Error", "binomialHeapError": "Binomial Heap Error", "leftistHeapError": "Leftist <PERSON><PERSON>", "skewHeapError": "Skew Heap Error", "pairingHeapError": "Pairing Heap Error", "dHeapError": "D-Heap Error", "binaryHeapError": "Binary Heap Error", "minHeapError": "<PERSON>r", "maxHeapError": "<PERSON>rror", "heapSortError": "<PERSON>ap Sort Error", "quickSortError": "Quick Sort Error", "mergeSortError": "<PERSON><PERSON>", "bubbleSortError": "Bubble Sort Error", "insertionSortError": "Insertion Sort Error", "selectionSortError": "Selection Sort Error", "shellSortError": "Shell Sort Error", "radixSortError": "<PERSON><PERSON><PERSON>", "countingSortError": "Counting <PERSON><PERSON>r", "bucketSortError": "Bucket Sort Error", "timSortError": "<PERSON>", "introSortError": "Intro Sort Error", "smoothSortError": "Smooth Sort Error", "stoogeSortError": "Stooge Sort Error", "bogoSortError": "Bogo Sort Error", "sleepSortError": "Sleep Sort Error", "pancakeSortError": "Pancake Sort Error", "cocktailSortError": "Cocktail Sort Error", "gnomeSortError": "Gnome Sort Error", "oddEvenSortError": "Odd-Even <PERSON><PERSON>", "combSortError": "Comb Sort Error", "pigeonholeSortError": "Pigeonhole Sort Error", "cycleSort": "Cycle Sort Error", "librarySort": "Library Sort Error", "patienceSort": "Patience Sort <PERSON>", "strandSort": "Strand Sort Error", "tagSort": "Tag Sort Error", "treeSort": "Tree Sort Error", "blockSort": "Block Sort Error", "flashSort": "<PERSON>rt Error", "postmanSort": "Postman <PERSON><PERSON>", "beadSort": "<PERSON>ad <PERSON>", "gravitySort": "Gravity Sort Error", "monkeySort": "Monkey Sort Error", "miracleSort": "Miracle Sort Error", "slowSort": "Slow Sort Error", "stalinSort": "<PERSON>", "quantumBogoSort": "Quantum Bogo Sort Error", "intelligentDesignSort": "Intelligent Design Sort Error", "panicSort": "Panic Sort Error", "destroyTheUniverseSort": "Destroy The Universe Sort Error"}, "mentorship": {"dashboard": {"title": "Mentorship Dashboard", "subtitle": "Manage your mentorship relationships and sessions", "refreshData": "Refresh data", "findMentor": "Find a <PERSON>tor", "upcomingSessions": "Upcoming Sessions", "scheduleNewSession": "Schedule New Session", "noUpcomingSessions": "No Upcoming Sessions", "noUpcomingSessionsDesc": "You don't have any upcoming mentorship sessions scheduled.", "scheduleSession": "Schedule a Session", "activeMentorships": "Active Mentorships", "noActiveMentorships": "No Active Mentorships", "noActiveMentorshipsDesc": "You don't have any active mentorship relationships yet.", "recentFeedback": "Recent Feedback", "noSessionHistory": "No Session History", "noSessionHistoryDesc": "You don't have any completed sessions yet. After completing sessions, you'll see feedback here."}}, "incubator": {"aiPowered": {"description": "Generate comprehensive business plans using advanced AI technology", "errorGenerating": "Error generating business plan. Please try again.", "planGenerated": "Business plan generated successfully!", "title": "AI-Powered Business Plan Generator"}}, "analytics": {"overview": "Overview", "metrics": "Metrics", "performance": "Performance", "trends": "Trends", "insights": "Insights", "reports": "Reports", "data": "Data", "statistics": "Statistics"}, "crud": {"create": "Create", "read": "Read", "update": "Update", "delete": "Delete", "list": "List", "view": "View", "edit": "Edit", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "success": "Success", "error": "Error", "loading": "Loading", "saving": "Saving", "deleting": "Deleting", "updating": "Updating", "creating": "Creating"}, "template.not.found": "Template Not Found", "the.requested.template": "The requested template could not be loaded.", "loading.template": "Loading template...", "about.this.template": "About This Template", "template.preview": "Template Preview", "sections": "Sections", "completion.rate": "Completion Rate", "est.time": "Est. Time", "rating": "Rating", "section.content.preview": "Section Content Preview", "guiding.questions": "Guiding Questions", "ai.assistance.available": "AI Assistance Available", "navigation": {"returnToHomepage": "Return to Homepage", "back": "Back", "next": "Next", "previous": "Previous"}, "confirmation": {"yes": "Yes", "no": "No", "ok": "OK"}, "forms": {"fields": {"email": "Email", "location": "Location", "description": "Description"}}, "misc": {"of": "of", "items": "items", "by": "by", "language": "Language", "changeLanguage": "Change Language", "user": "User"}, "rtl": {"direction": "ltr", "textAlign": "left", "marginStart": "margin-left", "marginEnd": "margin-right", "paddingStart": "padding-left", "paddingEnd": "padding-right", "flexDirection": "flex-row", "iconPosition": "right"}, "superAdmin": {"systemManagement": "System Management", "aiSystemManagement": "AI System Management", "aiConfiguration": "AI Configuration", "aiAnalytics": "AI Analytics", "aiMonitoring": "AI Monitoring", "userImpersonation": "User Impersonation", "systemLogs": "System Logs", "analytics": "Analytics", "security": "Security Center", "performance": "Performance Center", "api": "API Management", "communication": "Communication Center", "backup": "Backup Management", "monitoring": "System Monitoring", "advancedUsers": "Advanced User Management"}}