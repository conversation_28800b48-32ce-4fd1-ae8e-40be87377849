{"dashboard": {"title": "Dashboard", "welcome": "Welcome to your dashboard", "overview": "Overview", "profileCompletion": {"title": "Profile Completion", "juststarted": "Just getting started", "complete": "Complete", "almostthere": "Almost There", "makingprogress": "Making Progress", "completeYourProfile": "Complete Your Profile", "completion": "completion", "missingFields": "Missing Fields"}, "userActivity": {"title": "User Activity", "postsCreated": "Posts Created", "commentsMade": "Comments Made", "eventsOrganized": "Events Organized", "likesReceived": "<PERSON><PERSON> Re<PERSON>", "engagementScore": "Engagement Score", "activityRank": "Activity Rank", "recentActivity": "Recent Activity", "noRecentActivity": "No recent activity"}, "quickActions": {"title": "Quick Actions", "createPost": "Create Post", "createEvent": "Create Event", "viewProfile": "View Profile", "editProfile": "Edit Profile", "settings": "Settings"}, "aiEnhancedIdea": "🚀 AI-Enhanced Business Idea Creator", "aiEnhancedDescription": "Create business ideas with real-time AI assistance", "aiWorkflows": "⚡ AI Workflows Dashboard", "aiWorkflowsDescription": "Automated business plan generation", "predictiveIntelligence": "🔮 Predictive Intelligence Dashboard", "predictiveDescription": "Success predictions and risk assessment", "submitNewIdea": "Submit New Idea", "findMentor": "Find Mentor", "exploreResources": "Explore Resources", "exploreFunding": "Explore Funding", "createBusinessPlan": "Create Business Plan", "stats": {"totalPosts": "Total Posts", "totalEvents": "Total Events", "totalComments": "Total Comments", "totalLikes": "Total Likes", "followers": "Followers", "following": "Following", "totalIdeas": "Total Ideas", "approved": "Approved", "pending": "Pending", "rejected": "Rejected"}, "notifications": {"title": "Notifications", "markAllRead": "<PERSON> as <PERSON>", "noNotifications": "No notifications", "viewAll": "View All"}, "recentActivity": {"title": "Recent Activity", "noActivity": "No recent activity", "viewMore": "View More"}, "profile": {"information": "Profile Information"}, "first": {"name": "First Name"}, "last": {"name": "Last Name"}, "not": {"provided": "Not provided"}, "email": "Email", "location": "Location", "member": {"since": "Member Since"}, "unknown": "Unknown", "social": {"links": "Social Links"}, "using": {"redux": {"user": "Using Redux user data as fallback"}}, "contentRecommendations": {"title": "Content Recommendations", "noRecommendations": "No recommendations available at the moment. Check back later for personalized content suggestions."}, "section": {"title": "Section Title", "type": "Section Type"}, "text": "Text", "list": "List", "table": "Table", "chart": "Chart", "description": "Description", "enter": {"ai": {"prompt": "Enter AI prompt for generating content for this section..."}}}}